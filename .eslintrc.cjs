module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'plugin:vue/vue3-essential',
    'plugin:vue/vue3-recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:nuxt/recommended',
    'prettier',
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    es6: true,
    parser: '@typescript-eslint/parser',
  },
  plugins: ['simple-import-sort', '@typescript-eslint'],
  rules: {
    // 禁止console.log
    'no-console': ['error', { allow: ['warn', 'error'] }],
    // 禁止变量声明覆盖外层作用域的变量
    'no-shadow': ['error', { allow: ['state'] }],
    // 不可对方法的参数直接更改（可修改对象的属性）
    'no-param-reassign': ['error', { props: false }],
    // 关闭要求全局require
    'global-require': 'off',
    // 不使用includes而是indexOf !== -1 兼容IE
    'unicorn/prefer-includes': 'off',
    // 允许有未命名的function
    'func-names': 'off',
    'nuxt/no-cjs-in-config': 'off',
    'vue/no-v-html': 'off',
    'vue/multi-word-component-names': 'off',
    'simple-import-sort/imports': [
      'error',
      {
        groups: [
          [
            '^vue', // vue放在首行
            // 以字母(或数字或下划线)或“@”后面跟着字母开头的东西,通常为nodeModules引入
            '^@?\\w',
            '^@(/.*|$)', // 内部导入 "@/"
            '^\\.\\.(?!/?$)', // 父级导入. 把 `..` 放在最后.
            '^\\.\\./?$',
            // 同级导入. 把同一个文件夹.放在最后
            '^\\./(?=.*/)(?!/?$)',
            '^\\.(?!/?$)',
            '^\\./?$',
            '^.+\\.?(css|less|scss)$', // 样式导入.
            '^\\u0000', // 带有副作用导入，比如import 'a.css'这种.
          ],
        ],
      },
    ],
    'comma-dangle': ['error', 'always-multiline'],
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-non-null-assertion': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/no-empty-object-type': 'off',
    '@typescript-eslint/no-empty-interface': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    // '@typescript-eslint/no-unused-vars': ['warn', { varsIgnorePattern: '^_' }], // _开头的变量忽略该规则检查
    // note you must disable the base rule as it can report incorrect errors
    semi: 'off',
    '@typescript-eslint/member-delimiter-style': [
      'error',
      {
        multiline: {
          delimiter: 'semi', // 'none' | 'semi' | 'comma'
          requireLast: true,
        },
        singleline: {
          delimiter: 'semi',
          requireLast: false,
        },
      },
    ],
    'no-shadow': 'off',
  },
  overrides: [
    {
      files: ['*.ts'],
      parser: '@typescript-eslint/parser',
    },
  ],
}
