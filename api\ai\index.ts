import urls from '@/api/urls'
import { get, post } from '@/composables/useFetch.ts'

/**
 * ai 首页
 */
export async function aiChatIndexApi() {
  try {
    const { success, data, message } = await get(urls.ai.index.chat_index)()
    if (!success) {
      return { success: false, message, data: {} }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, message: error, data: {} }
  }
}

/**
 * ai 获取窗口历史记录
 */
export async function getChatHistoryApi(params: { session_id: string }) {
  try {
    const { success, data, message } = await get(urls.ai_v1.getChatHistory)(
      params,
    )
    if (!success) {
      return { success: false, message }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, data: [], message: error }
  }
}

/**
 * ai 获取历史对话列表
 */
export async function getSessionListApi(params: {
  page: number;
  page_size: number;
}) {
  try {
    const { success, data, message } = await get(urls.ai_v1.getSessionList)(
      params,
    )
    if (!success) {
      return { success: false, message, data: [] }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, data: [], message: error }
  }
}

/**
 * ai 删除 session
 */
export async function deleteSessionApi(params: { session_id: string }) {
  try {
    const { success, message } = await get(urls.ai_v1.deleteSession)(params)
    if (!success) {
      return { success: false, message }
    }
    return { success: true }
  } catch (error) {
    console.error(error)
    return { success: false, message: error }
  }
}

/**
 * ai 评价回答
 */
export async function userEvaluateApi(params: {
  mark: string;
  session_id: string;
  assistant_id: string;
}) {
  try {
    const { success, message } = await post(urls.ai_v1.evaluate)(params)
    if (!success) {
      return { success: false, message }
    }
    return { success: true }
  } catch (e) {
    console.error(e)
    return { success: false, message: e }
  }
}

/**
 * ai 获取更多材料
 */
export async function getMoreNoteApi(params: {
  cache_key: string;
  page: number;
  size: number;
}) {
  try {
    const { success, data, message } = await get(urls.ai.getMoreNote)(params)
    if (!success) {
      return { success: false, message }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, message: error }
  }
}
