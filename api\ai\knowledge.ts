import urls from '@/api/urls'
import { get, post } from '@/composables/useFetch.ts'

/**
 * 获取知识库列表（资源广场）
 */
export async function getKnowledgeResourceListApi(params: {
  page: number;
  limit: number;
}) {
  try {
    const { success, data, message } = await get(urls.ai.knowledge.resource)(
      params,
    )
    if (!success) {
      return { success: false, message, data: {} }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, message: error, data: {} }
  }
}

/**
 * 获取知识库列表 all
 */
export async function getKnowledgeMineListApi(params: {
  page: number;
  limit: number;
}) {
  try {
    const { success, data, message } = await get(urls.ai.knowledge.mine_list)(
      params,
    )
    if (!success) {
      return { success: false, message, data: {} }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, message: error, data: {} }
  }
}

/**
 * 获取我的关注知识库列表
 */
export async function getKnowledgeMineJoinedListApi(params: {
  page: number;
  limit: number;
}) {
  const { success, data, message } = await get(
    urls.ai.knowledge.mine_follow_list,
  )(params)
  if (!success) {
    return { success: false, message, data: {} }
  }
  return { success: true, data }
}

/**
 * 获取我的创建知识库列表
 */
export async function getKnowledgeMineCreateListApi(params: {
  page: number;
  limit: number;
}) {
  const { success, data, message } = await get(
    urls.ai.knowledge.mine_create_list,
  )(params)
  if (!success) {
    return { success: false, message, data: {} }
  }
  return { success: true, data }
}

/**
 * 创建知识库
 */
export async function createKnowledgeApi(params: any) {
  const { success, data, message } = await post(urls.ai.knowledge.create)(
    params,
  )
  if (!success) {
    return { success: false, message, data: {} }
  }
  return { success: true, data }
}

/**
 * 设置默认知识库
 */
export async function setDefaultKnowledgeApi(params: any) {
  const { success, message } = await post(urls.ai.knowledge.default)(params)
  if (!success) {
    return { success: false, message }
  }
  return { success: true, message }
}

/**
 * 发布知识库
 */
export async function setKnowledgePublishApi(params: any) {
  const { success, message } = await get(urls.ai.knowledge.publish)(params)
  if (!success) {
    return { success: false, message }
  }
  return { success: true, message }
}

/**
 * 取消发布知识库
 */
export async function setKnowledgeUnpublishApi(params: any) {
  const { success, message } = await get(urls.ai.knowledge.unpublish)(params)
  if (!success) {
    return { success: false, message }
  }
  return { success: true, message }
}

/**
 * 知识库详情
 */
export async function getKnowledgeDetailApi(params: any) {
  const { success, data, message } = await get(urls.ai.knowledge.detail)(params)
  if (!success) {
    return { success: false, message, data: {} }
  }
  return { success: true, data }
}

/**
 * 知识库成员
 */
export async function getKnowledgeMemberListApi(params: any) {
  const { success, data, message } = await get(urls.ai.knowledge.member)(params)
  if (!success) {
    return { success: false, message, data: [] }
  }
  return { success: true, data: data }
}

/**
 * ai 获取知识库列表
 */
export async function getKnowledgeFileListApi(params: {
  page: number;
  limit: number;
  knowledge_id: string;
}) {
  const defaultData = {
    list: [],
    total: 0,
    hasMore: false,
  }
  try {
    const { success, data, message } = await get(urls.ai.knowledge.document)(
      params,
    )
    if (!success) {
      return { success: false, message, data: defaultData }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, message: error, data: defaultData }
  }
}

/**
 * ai 知识库 上传文件
 */
export async function uploadKnowledgeFileApi(params: { file: FormData }) {
  try {
    const { success, message } = await post(urls.ai.knowledge.upload)(params)
    if (!success) {
      return { success: false, message }
    }
    return { success: true }
  } catch (error) {
    console.error(error)
    return { success: false, message: error }
  }
}

/**
 * ai 知识库 删除文件
 */
export async function deleteKnowledgeFileApi(params: {
  document_id: string;
  knowledge_id: string;
}) {
  try {
    const { success, message } = await post(urls.ai.knowledge.delete)(params)
    if (!success) {
      return { success: false, message }
    }
    return { success: true }
  } catch (error) {
    console.error(error)
    return { success: false, message: error }
  }
}
