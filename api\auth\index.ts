import urls from '@/api/urls'
import { getGetModel, getPostModel } from '@/composables/useHttp'

export const LOGIN_TAG = {
  EMAIL_PASSWORD: 2, // 邮箱+密码
}
export const REGISTER_TAG = {
  EMAIL: 1, // 邮箱
  EMAIL_SMS: 2, // 邮箱发送验证码
}
export const loginRequest = getPostModel(urls.auth.login) // 登录

export const registerSendRequest = getPostModel(urls.auth.register.send) // 注册发送

export const registerRequest = getPostModel(urls.auth.register.index) // 完成注册

export const userInfoRequest = getGetModel(urls.user.info) // 获取用户信息

export const logoutRequest = getGetModel(urls.auth.logout) // 注销
