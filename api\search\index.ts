import urls from '@/api/urls.ts'
import { get } from '@/composables/useFetch.ts'

/**
 * 获取搜索筛选数据
 */
export async function getSearchFilter() {
  try {
    const { success, data, message } = await get('/search/filter')()
    if (!success) {
      return { success: false, message, data: {} }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, message: error, data: {} }
  }
}

/**
 * 获取搜索热门数据
 */
export async function getSearchHottest() {
  try {
    const { success, data, message } = await get('/search/hottest')()
    if (!success) {
      return { success: false, message, data: {} }
    }
    return { success: true, data }
  } catch (error) {
    console.error(error)
    return { success: false, message: error, data: {} }
  }
}