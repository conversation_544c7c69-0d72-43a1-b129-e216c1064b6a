import urls from '@/api/urls'
import { post } from '@/composables/useFetch.ts'

/**
 * 获取搜索筛选数据
 */
export async function userHelpRequest(params: any) {
    try {
        const { success, data, message } = await post(urls.user.help)(params)
        if (!success) {
            return { success: false, message }
        }
        return { success: true, data }
    } catch (error) {
        console.error(error)
        return { success: false, message: error }
    }
}