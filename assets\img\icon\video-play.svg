<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="42.208" viewBox="0 0 40 42.208">
  <defs>
    <style>.a{fill:rgba(0,0,0,0.3);}.b{fill:#fff;}.c{filter:url(#a);}</style>
    <filter id="a" x="4.499" y="3.954" width="34.571" height="38.254" filterUnits="userSpaceOnUse">
      <feOffset dy="3"></feOffset>
      <feGaussianBlur stdDeviation="3" result="b"></feGaussianBlur>
      <feFlood flood-opacity="0.161"></feFlood>
      <feComposite operator="in" in2="b"></feComposite>
      <feComposite in="SourceGraphic"></feComposite>
    </filter>
  </defs>
  <g transform="translate(-148.147 -257.147)">
    <circle class="a" cx="20" cy="20" r="20" transform="translate(148.147 257.147)"></circle>
    <g class="c" transform="matrix(1, 0, 0, 1, 148.15, 257.15)">
      <path class="b" d="M142.735,19.594c2.062,1.306,2.061,3.424,0,4.729l-11.291,7.149c-2.062,1.306-3.734.456-3.734-1.9V14.341c0-2.353,1.673-3.2,3.734-1.9Z" transform="translate(-114.21 -1.88)"></path>
    </g>
  </g>
</svg>