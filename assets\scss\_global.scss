@charset "UTF-8";
// 把默认边距去了
body,
html,
#__nuxt,
#__layout {
  padding: 0;
  margin: 0;
  min-width: 1200px;
  width: 100%;
  height: 100%;
  // min-height: 100vh;
  scroll-behavior: smooth;
  // overflow: visible !important;
  // 处理elementUI 点击el-image会给body添加overflow: hidden;导致无法滚动的bug。也可以考虑把elementUI版本升至2.13.1往后
  font-variant-ligatures: no-common-ligatures;
}

div {
  box-sizing: border-box;
}

input,
textarea {
  outline: none; // 去除选中状态边框
  border: none;
  background-color: rgba(0, 0, 0, 0); // 透明背景
  resize: none;
}

button {
  color: #ffffff;
  border: 0 none;
  background: #f18d02;
  outline: none;
}

#nprogress .bar {
  height: 2px;
  background-color: #ff6d00;
}

// element-plus样式
.el-loading-spinner .path {
  stroke: #ff6d00 !important;
}
.el-pagination.is-background .el-pager li:not(.disabled) {
  &:hover {
    color: #ff6d00;
  }
  &.active {
    background-color: #ff6d00;
    color: #ffffff;
  }
}
.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  &:hover {
    color: #ff6d00;
  }
}

.el-pagination.is-background .btn-next.is-active,
.el-pagination.is-background .btn-prev.is-active,
.el-pagination.is-background .el-pager li.is-active {
  background-color: #ff6d00 !important;
  color: #ffffff;
  &:hover {
    color: #ffffff;
  }
}

.el-pagination {
  width: fit-content !important;
  margin-bottom: 30px;
  .el-pagination__total {
    margin-left: 30px;
  }
  .is-active {
    background-color: #ff6d00;
  }
}

//  {
//   border-color: #ff6d00;
// }

.el-select .el-input__inner:focus,
.el-select .el-input.is-focus .el-input__inner,
.el-pagination__sizes .el-input .el-input__inner:hover {
  border-color: #ff6d00;
}
.el-select-dropdown__item.selected {
  color: #ff6d00;
}
.el-input .el-input__inner:focus,
.el-input .el-input.is-focus .el-input__inner,
.el-input .el-input__inner:hover {
  border-color: #ff6d00;
}

.el-textarea__inner:focus {
  box-shadow: 0 0 0 1px #ff6d00 inset;
}

.el-input {
  --el-input-focus-border-color: #ff6d00;
  .el-input__wrapper {
    height: 40px;
    background-color: #ffffff;
    border-radius: 8px;
    .el-input__inner {
      // font-family: Microsoft YaHei, sans-serif;
      font-size: 16px;
      color: #333333;
      &::placeholder {
        // font-family: Poppins, sans-serif;
        font-size: 14px;
        font-weight: normal;
        letter-spacing: 0px;
        color: #666666;
      }
    }
  }
}
.el-form-item {
  margin-bottom: 0;
}
.el-form-item__error {
  // font-family: Poppins, sans-serif;
  font-size: 13px;
  font-weight: 300;
  color: #ff4400;
  padding-top: 4px;
}
.el-select {
  --el-color-primary: #ff6d00;
}
.el-scrollbar {
  --el-color-primary: #ff6d00;
}

.el-dropdown-menu .el-dropdown-menu__item {
  text-align: center;
}
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: #f4f4f4 !important;
  color: #ff6d00;
}

.el-checkbox__inner {
  border-radius: 4px;
  &:after {
    border-radius: 1px;
  }
}
.el-checkbox__inner:hover {
  border-color: #f18d02;
}
.el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: #dcdfe6;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #f18d02;
  border-color: #f18d02;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #f18d02;
  border-color: #f18d02;
}

.el-message {
  z-index: 999999 !important;
}

.el-popover {
  min-width: 100px;
}

a {
  text-decoration: none;
}

.el-dialog {
  width: min-content;
  background: transparent;
  box-shadow: none;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    background: transparent;
  }
}

.el-message-override {
  z-index: 3000 !important;
}

.el-switch.is-checked .el-switch__core {
  border-color: #f18d02;
  background-color: #f18d02;
}

// 塑库自定义样式
.sokoo-button-default {
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  height: 40px;
  border-radius: 6px;
  background: #ff6600;
  // font-family: Poppins, sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
  &:hover {
    background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.2)
      ),
      #ff6600;
  }
  &:active {
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)),
      #ff6600;
  }
}
.sokoo-button-disabled {
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  height: 40px;
  border-radius: 6px;
  background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.5),
      rgba(255, 255, 255, 0.5)
    ),
    #ff6600;
  // font-family: Poppins, sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
  cursor: auto;
  &:hover {
    background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0.5)
      ),
      #ff6600;
  }
  &:active {
    background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0.5)
      ),
      #ff6600;
  }
}
.sokoo-button-unselected {
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  height: 40px;
  border-radius: 6px;
  background: #dedede;

  // font-family: Poppins, sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #999999;
}
.sokoo-button-large {
  height: 52px;
  // font-family: Poppins, sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #ffffff;
  border-radius: 8px;
}

.sokoo-send-sms-button {
  border-radius: 4px;
  background: #ffffff;
  border: 1px solid #dddddd;
  // font-family: Microsoft YaHei, sans-serif;
  font-size: 14px;
  font-weight: normal;
  color: #333333;
  cursor: pointer;
  .content-text {
    // font-family: Microsoft YaHei, sans-serif;
    font-size: 14px;
    font-weight: normal;
    color: #333333;
  }
  &:hover {
    .content-text {
      color: #ff6600;
    }
  }
  &:active {
    .content-text {
      color: #be5922;
    }
  }
}
.sokoo-send-sms-button-counting {
  background: #dedede;
  border: 1px solid #dddddd;
  cursor: auto;
  .count-text {
    // font-family: Microsoft YaHei, sans-serif;
    font-size: 14px;
    color: #ff6600;
    margin-right: 4px;
  }
  .unit-text {
    // font-family: Microsoft YaHei, sans-serif;
    font-size: 14px;
    color: #666666;
  }
}
.sokoo-send-sms-button-resend {
  background-color: #ffffff;
  color: #4e6ded;
  border: 1px solid #dddddd;
  .content-text {
    color: #4e6ded;
  }
}
.sokoo-link-text {
  color: #ff6600;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.sokoo-form-input-label {
  // font-family: Poppins, sans-serif;
  font-size: 16px;
  font-weight: normal;
  line-height: 18.72px;
  color: #333333;
  margin-bottom: 4px;
  user-select: none;
}
.sokoo-input-large {
  .el-input__wrapper {
    height: 52px;
  }
}

.el-message-box {
  .el-button {
    background-color: #ff6600;
    border: none;
    &:hover {
      background-color: lighten(#ff6600, 10%);
    }
    &:active {
      background-color: darken(#ff6600, 10%);
    }
    &:focus-visible {
      outline-color: lighten(#ff6600, 10%);
    }
  }
  .el-message-box__close {
    &:hover {
      color: #ff6600;
    }
  }
  .el-message-box__headerbtn {
    &:hover {
      .el-message-box__close {
        color: #ff6600;
      }
    }
  }
  .el-message-box__message p {
    text-align: justify;
  }
}

.el-overlay {
  z-index: 99999 !important;
}
