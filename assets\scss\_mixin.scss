@mixin text-one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin text-multi-line($number) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $number;
  -webkit-box-orient: vertical;
}

@mixin flex-all-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin overflow-ellipsis($line: 2) {
  overflow: hidden;
  // 将对象作为弹性伸缩盒子模型显示
  display: -webkit-box;
  // 设置子元素排列方式
  // autoprefixer: ignore next
  -webkit-box-orient: vertical;
  // 设置显示的行数，多出的部分会显示为...
  -webkit-line-clamp: 2;
  -webkit-line-clamp: $line;
}

@mixin single-overflow-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin svg-color($color, $distance: 80px) {
  position: relative;
  left: -$distance;
  filter: drop-shadow($color $distance 0);
}

@mixin scrollbar-style($width: 3px, $track: #ccc, $thumb: #888888) {
  &::-webkit-scrollbar {
    width: 3px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 3px;
    background-color: $track;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #888888;
  }
}
