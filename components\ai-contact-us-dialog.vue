<template>
  <el-dialog
    :model-value="props.visible"
    width="560px"
    :show-close="false"
    :append-to-body="true"
    @close="closeDialog"
  >
    <div class="dialog-content">
      <img
        src="@/assets/img/auth/register/close.svg"
        class="close-btn"
        alt="Close"
        @click="closeDialog"
      />
      <div class="dialog-title">Contact Us for Assistance</div>
      <div class="description">
        Detailed problem description helps experts devise a solution
      </div>
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
        <el-form-item label="Problem Description" prop="problem">
          <el-input v-model="form.problem" type="textarea" :maxlength="200" />
        </el-form-item>
        <el-form-item label="Work email" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <div class="create" @click="submit">Submit</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'

const props = defineProps<{
  visible: boolean;
  knowledgeId?: string;
}>()
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>()

const { $SKToast } = useNuxtApp()

const formRef = ref<FormInstance>()
const form = ref({
  problem: '',
  email: '',
})
const rules = {
  problem: [{ required: true, message: 'Please fill in' }],
  email: [
    { required: true, message: 'Please enter a valid email address' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!isEmail(value)) {
          callback(new Error('Please enter valid email address'))
          return false
        }
        callback()
      },
    },
  ],
}

const closeDialog = () => {
  form.value.problem = ''
  form.value.email = ''
  formRef.value?.resetFields()
  emit('update:visible', false)
}

const submit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) return
    closeDialog()
    $SKToast.warning('Submission Successful')
  })
}
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  padding: 0;
  background: transparent;
  .el-dialog__header {
    display: none;
  }
}
.dialog-content {
  width: 560px;
  border-radius: 16px;
  padding: 32px;
  box-sizing: border-box;
  background: #ffffff;
  position: relative;

  .close-btn {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
  }

  .dialog-title {
    font-size: 24px;
    font-weight: bold;
    color: #171a23;
    margin-bottom: 5px;
  }
  .description {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 24px;
  }

  .el-form-item {
    margin-bottom: 20px;
  }
  :deep(.el-form-item__label) {
    font-size: 14px;
    color: #171a23;
  }

  :deep(.el-textarea__inner) {
    height: 120px;
    resize: none;
  }
  :deep(.el-input__count) {
    background: transparent;
  }

  :deep(.el-input__wrapper),
  :deep(.el-textarea__inner) {
    border-radius: 8px;
    border: none;
    box-shadow: none;
    background: #f7f8fc;
  }
  .el-input,
  .el-textarea {
    border-radius: 8px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    &:hover,
    &:focus,
    &:focus-within {
      border: 1px solid #d9d9d9;
    }
  }

  .cover-upload {
    width: 60px;
    height: 60px;
  }
  .cover-upload-trigger {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    user-select: none;
    cursor: pointer;
    position: relative;
    &:hover {
      .mask {
        opacity: 1;
      }
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px;
      opacity: 0;
      transition: all 0.3s ease;
      z-index: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    gap: 16px;
    .create {
      width: 160px;
      height: 40px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;

      font-size: 14px;
      font-weight: bold;
      color: #6c6f76;
      user-select: none;
      cursor: pointer;
      transition: all 0.3s ease;

      color: #ffffff;
      background: #ff6600;
      &:hover {
        background: lighten($color: #ff6600, $amount: 10);
      }
      &:active {
        background: darken($color: #ff6600, $amount: 10);
      }
    }
  }
}
</style>
