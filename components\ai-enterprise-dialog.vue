<template>
  <el-dialog
    :model-value="props.visible"
    :show-close="false"
    :append-to-body="true"
    :destroy-on-close="true"
    :fullscreen="true"
    :z-index="2000"
    @close="closeDialog"
  >
    <div class="enterprise">
      <img
        src="@/assets/img/auth/register/close.svg"
        class="close-btn"
        alt="Close"
        @click="closeDialog"
      />
      <div class="title">Contact Our Sales Team</div>
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
        <el-form-item label="Company name" prop="companyName">
          <el-input v-model="form.companyName" />
        </el-form-item>
        <div class="name-container">
          <el-form-item label="First Name" prop="firstName">
            <el-input v-model="form.firstName" />
          </el-form-item>
          <el-form-item label="Last Name" prop="lastName">
            <el-input v-model="form.lastName" />
          </el-form-item>
        </div>
        <el-form-item label="Work email" prop="email">
          <el-input v-model="form.email" />
        </el-form-item>
        <el-form-item label="Job title" prop="jobTitle">
          <el-input v-model="form.jobTitle" />
        </el-form-item>
        <el-form-item
          label="Problem Description"
          prop="problem"
          class="message-item"
        >
          <el-input v-model="form.problem" type="textarea" />
        </el-form-item>
      </el-form>
      <div class="submit-btn" @click="handleSubmit">Submit</div>
    </div>
  </el-dialog>

  <el-dialog
    :model-value="submitSuccessVisible"
    width="400px"
    :show-close="false"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :z-index="1000"
    @close="closeSuccessDialog"
  >
    <div class="submit-success">
      <img
        src="@/assets/img/auth/register/close.svg"
        class="close-btn"
        alt="Close"
        @click="closeSuccessDialog"
      />
      <img
        src="@/assets/img/ai/success-img.svg"
        alt="success"
        class="success-img"
      />
      <div class="title">Successfully</div>
      <div class="desc">我们的客服人员将很快与您联系。</div>
      <div class="submit-btn" @click="closeSuccessDialog">确认</div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { FormInstance } from 'element-plus'

const props = defineProps<{
  visible: boolean;
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>()

const form = ref({
  companyName: '',
  firstName: '',
  lastName: '',
  email: '',
  jobTitle: '',
  problem: '',
})

const rules = ref({
  companyName: [{ required: true, message: 'Please enter your company name' }],
  firstName: [{ required: true, message: 'Please enter your first name' }],
  lastName: [{ required: true, message: 'Please enter your last name' }],
  email: [
    { required: true, message: 'Please enter your work email' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (!isEmail(value)) {
          callback(new Error('Please enter valid email address'))
          return false
        }
        callback()
      },
    },
  ],
  jobTitle: [{ required: true, message: 'Please enter your job title' }],
})

const formRef = ref<FormInstance>()
const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) return
    submitSuccessVisible.value = true
    setTimeout(() => {
      closeDialog()
    }, 100)
  })
}

const closeDialog = () => {
  emit('update:visible', false)
  form.value.companyName = ''
  form.value.firstName = ''
  form.value.lastName = ''
  form.value.email = ''
  form.value.jobTitle = ''
  form.value.problem = ''
  formRef.value?.resetFields()
}

const submitSuccessVisible = ref(false)
const closeSuccessDialog = () => {
  submitSuccessVisible.value = false
}
</script>

<style lang="scss">
.is-fullscreen {
  padding: 0 !important;
  background: transparent;
}
</style>

<style lang="scss" scoped>
.enterprise {
  height: 100vh;
  width: 100vw;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  .close-btn {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 32px;
    right: 32px;
    cursor: pointer;
    z-index: 5;
  }

  .title {
    margin: 80px 0 40px 0;
    width: 500px;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 40px;
  }
  .el-form {
    width: 500px;
    .el-form-item {
      width: 100%;
      margin-bottom: 20px;
    }
    .el-input {
      width: 100%;
    }
    .name-container {
      display: flex;
      gap: 16px;
    }

    :deep(.el-textarea__inner) {
      height: 120px;
      resize: none;
    }

    :deep(.el-input__wrapper),
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: none;
      box-shadow: none;
      background: #f7f8fc;
    }
    .el-input,
    .el-textarea {
      border-radius: 8px;
      border: 1px solid transparent;
      transition: all 0.3s ease;
      &:hover,
      &:focus,
      &:focus-within {
        border: 1px solid #d9d9d9;
      }
    }
  }

  .submit-btn {
    width: 500px;
  }
}

.submit-success {
  width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 16px;
  padding: 30px;
  position: relative;

  .close-btn {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
    z-index: 5;
  }

  .success-img {
    width: 86px;
    margin-bottom: 16px;
  }

  .title {
    margin: 8px 0 16px 0;
    font-size: 18px;
    color: #252c58;
    text-shadow: 0px 4px 16px rgba(173, 190, 215, 0.25);
  }

  .desc {
    margin-bottom: 24px;
    font-size: 14px;
    color: #86909c;
    text-shadow: 0px 4px 16px rgba(173, 190, 215, 0.25);
  }

  .submit-btn {
    width: 172px;
  }
}

.submit-btn {
  height: 40px;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
  background: #ff6600;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.075, 0.82, 0.165, 1);
  &:hover {
    background: lighten($color: #ff6600, $amount: 10%);
  }
  &:active {
    background: darken($color: #ff6600, $amount: 10%);
  }
}
</style>
