<template>
  <el-dialog
    :model-value="props.visible"
    :show-close="false"
    :append-to-body="true"
    :destroy-on-close="true"
    :z-index="1999"
    width="960px"
    @close="closeDialog"
  >
    <div class="dialog-content">
      <img
        src="@/assets/img/auth/register/close.svg"
        class="close-btn"
        alt="Close"
        @click="closeDialog"
      />
      <div class="gradient-header">Upgrade plan</div>
      <div class="plans-container">
        <div
          v-for="plan in plans"
          :key="plan.name"
          class="plan-card"
          :class="`${plan.name}-card`"
        >
          <div class="plan-card-header">
            <div class="plan-card-title">{{ plan.name }}</div>
            <div class="price">${{ plan.price }} <span>/month</span></div>
            <div class="btn" @click="plan.btnClick()">{{ plan.btnText }}</div>
          </div>
          <div class="feature-list">
            <div
              v-for="feature in plan.features"
              :key="feature.name"
              class="feature-item"
            >
              <span class="feature-name">{{ feature.name }}</span>
              <span
                class="feature-value"
                :class="{ highlight: feature.isHighlight }"
                >{{ feature.value }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
const props = defineProps<{
  visible: boolean;
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'enterprise'): void;
}>()

const closeDialog = () => {
  emit('update:visible', false)
}

const handleContactUs = () => {
  emit('enterprise')
}

const plans = [
  {
    name: 'Free',
    price: 0,
    btnText: 'Current Plan',
    btnClick: () => {},
    features: [
      { name: '单文件最大上传', value: '5MB' },
      { name: '知识库存储空间', value: '100MB' },
      { name: '可创建知识库', value: '2 个' },
      { name: 'AI大模型', value: '1 个' },
    ],
  },
  {
    name: 'Premier',
    price: 20,
    btnText: 'Upgrade',
    btnClick: () => {},
    features: [
      { name: '单文件最大上传', value: '50MB', isHighlight: true },
      { name: '知识库存储空间', value: '2G', isHighlight: true },
      { name: '可创建知识库', value: '不限', isHighlight: true },
      { name: 'AI大模型', value: '全部', isHighlight: true },
      { name: '专属客户经理', value: '标准' },
    ],
  },
  {
    name: 'Enterprise',
    price: 0,
    btnText: 'Contact Sales',
    btnClick: () => {
      handleContactUs()
    },
    features: [
      { name: '单文件最大上传', value: '不限' },
      { name: '知识库存储空间', value: '不限' },
      { name: '可创建知识库', value: '不限' },
      { name: 'AI大模型', value: '全部' },
      { name: 'SAML SSO 单点登录', value: '支持' },
      { name: '专属客户经理', value: '7 × 12小时' },
    ],
  },
]
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  padding: 0;
  background: transparent;
  .el-dialog__header {
    display: none;
  }
}
.dialog-content {
  width: 960px;
  border-radius: 16px;
  padding: 24px 32px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background: linear-gradient(20deg, #ffffff 45%, #ffcfb6 120%);
  background-blend-mode: overlay;
  background-size: 150% 150%;
  overflow: hidden;
  position: relative;

  .close-btn {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
    z-index: 5;
  }

  .gradient-header {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 35px;
    font-size: 24px;
    font-weight: bold;
    color: #ff6600;
  }

  .plans-container {
    display: flex;
    justify-content: space-between;

    .plan-card {
      width: 288px;
      border-radius: 12px;

      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: #ffffff;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      border: 1px solid #f3f5f7;

      .plan-card-header {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        padding: 24px 24px 18px 24px;
        box-sizing: border-box;

        .plan-card-title {
          height: 35px;
          margin-bottom: 12px;
          font-size: 24px;
        }

        .price {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 12px;
          color: #171a23;

          span {
            font-size: 16px;
            font-weight: normal;
          }
        }

        .btn {
          width: 100%;
          height: 40px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: 500;
          background: #ffffff;
          user-select: none;
          cursor: pointer;
        }
      }

      .feature-list {
        width: 100%;
        padding: 0 24px;

        .feature-item {
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex-shrink: 0;
          &:not(:last-child) {
            border-bottom: 1px solid #e7e8ec;
          }

          .feature-name {
            color: #606266;
          }

          .feature-value {
            font-weight: 500;

            &.highlight {
              color: #ff6b00;
            }
          }
        }
      }
    }

    .Free-card {
      .plan-card-header {
        .btn {
          background-color: #f5f7fa;
          color: #909399;
          border: 2px solid #f3f5f7;
          box-sizing: border-box;
        }
      }
    }

    .Premier-card {
      .plan-card-header {
        background: linear-gradient(270deg, #e52f2f 0%, #982df4 100%);
        .price,
        .plan-card-title {
          color: #ffffff;
        }
        .btn {
          color: #982ef5;
        }
        .premier {
          background: #ffffff;
        }
      }
    }

    .Enterprise-card {
      .plan-card-header {
        .plan-card-title {
          color: #ff6600;
          font-weight: bold;
        }
        .btn {
          color: #ffffff;
          background: linear-gradient(270deg, #e52f2f 0%, #982df4 100%);
        }
      }
    }
  }
}
</style>
