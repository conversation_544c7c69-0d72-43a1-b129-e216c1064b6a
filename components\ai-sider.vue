<template>
  <div>
    <div class="ai-sider-container">
      <div class="ai-logo">
        <img class="logo" src="@/assets/img/ai/ai-logo.svg" />
        <div class="title">Plasdata AI</div>
      </div>
      <div class="chat-type-list">
        <nuxt-link
          v-for="(item, index) in toolsList"
          :key="index"
          :to="`/${item.type}`"
          :class="{
            'type-item': true,
            active: route.name === item.type,
            'new-chat': item.type === TOOL_TYPE.CHAT,
          }"
        >
          <img :src="item.icon" />
          <span>{{ item.name }}</span>
        </nuxt-link>
      </div>
      <template v-if="isLogin">
        <el-collapse v-model="sessionVisiable" accordion :style="{ opacity: sessionList.length > 0 ? 1 : 0 }">
          <el-collapse-item title="Chat History" name="History">
            <div
              ref="sessionListRef"
              class="session-list"
              @scroll="handleScroll"
            >
              <div
                v-for="(item, index) in sessionList"
                :key="index"
                class="session-item"
                :class="{
                  active: item.session_id === aiStore.currentSessionId,
                }"
                :title="item.title"
                @click="onSelectSession(item.session_id)"
              >
                {{ item.title }}
                <div
                  class="delete"
                  @click.stop="onDeleteSession(item.session_id)"
                >
                  <img src="@/assets/img/ai/delete-icon.svg" alt="Delete" />
                </div>
              </div>
              <div v-if="sessionStatusText" class="session-list-status">
                {{ sessionStatusText }}
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <div class="capacity-block">
          <div class="header-line">
            <div class="text">12.34MB / 100MB</div>
            <img
              class="img"
              src="@/assets/img/ai/knowledge/capacity-icon.svg"
              @click="handlePricingDialog"
            />
          </div>
          <div class="storage-progress">
            <el-progress :percentage="20" :show-text="false" color="#FF6600" />
          </div>
        </div>
      </template>
    </div>
    <aiPricingDialog
      v-model:visible="pricingDialogVisible"
      @enterprise="handleEnterpriseDialog"
    />
    <aiEnterpriseDialog v-model:visible="enterpriseDialogVisible" />
  </div>
</template>

<script setup lang="ts">
import aiEnterpriseDialog from '@//components/ai-enterprise-dialog.vue'
import aiPricingDialog from '@//components/ai-pricing-dialog.vue'
import { deleteSessionApi, getSessionListApi } from '@/api/ai'
import KnowledgeIcon from '@/assets/img/ai/tools/knowledge-icon.svg'
import MaterialIcon from '@/assets/img/ai/tools/material-icon.svg'
import ChatIcon from '@/assets/img/ai/tools/new-icon.svg'
import ResourceIcon from '@/assets/img/ai/tools/resource-icon.svg'
import { useLocalStorage } from '@/composables/useLocalStorage/index.js'
import { useAiStore } from '@/store/ai'
import { useUserStore } from '@/store/user'
import { TOOL_TYPE } from '@/types/ai/enums'
import type { HistoryItem, ToolItem } from '@/types/ai/interface'

const { useStorage } = useLocalStorage()
const { $SKToast, $SKConfirm } = useNuxtApp()

const route = useRoute()
const aiStore = useAiStore()
const userStore = useUserStore()
const isLogin = computed(() => userStore.isLogin)



const pricingDialogVisible = ref(false)
const handlePricingDialog = () => {
  pricingDialogVisible.value = true
}

const enterpriseDialogVisible = ref(false)
const handleEnterpriseDialog = () => {
  enterpriseDialogVisible.value = true
}

const toolsList: Ref<ToolItem[]> = ref([
  {
    type: TOOL_TYPE.CHAT,
    name: 'NEW Chat', // AI问答
    icon: ChatIcon,
  },
  {
    type: TOOL_TYPE.RESOURCE,
    name: 'Resource Hub', // 资源广场
    icon: ResourceIcon,
  },
  {
    type: TOOL_TYPE.MATERIAL,
    name: 'Material Search', // 材料搜索
    icon: MaterialIcon,
  },
  {
    type: TOOL_TYPE.KNOWLEDGE,
    name: 'Knowledge Bases', // 知识库
    icon: KnowledgeIcon,
  },
])


// 历史会话记录
const sessionVisiable = ref('History')
const sessionListRef = ref<HTMLElement>()
type SessionState = {
  page: number;
  pageSize: number;
  loading: boolean;
}
const sessionState = reactive<SessionState>({
  page: 1,
  pageSize: 10,
  hasMore: true,
  loading: false,
})
const sessionList = useStorage<HistoryItem[]>('sessionList', [])
const sessionStatusText = computed(() => {
  if (sessionState.loading) return 'Loading...'
  if (!sessionState.hasMore && sessionList.value.length > 0)
    return 'No more history'
  return ''
})

// 加载会话列表
const loadSessionList = async (reset = false) => {
  if (sessionState.loading || (!sessionState.hasMore && !reset)) return
  sessionState.loading = true
  try {
    // 重置列表状态
    if (reset) {
      sessionState.page = 1
      sessionState.hasMore = true
      sessionList.value = []
    }

    // 请求数据
    const { data } = await getSessionListApi({
      page: sessionState.page,
      page_size: sessionState.pageSize,
    })

    const list = data.list || []
    sessionState.hasMore = data.has_more

    // 更新分页状态
    if (list.length >= sessionState.pageSize) sessionState.page++

    // 更新列表数据
    sessionList.value = [...sessionList.value, ...list]
  } catch (error) {
    console.error('Failed to load session list:', error)
  } finally {
    sessionState.loading = false

    // 检查是否需要填充更多数据
    if (sessionState.hasMore) {
      nextTick(checkAndLoadMore)
    }
  }
}

// 自动填充容器
const checkAndLoadMore = () => {
  if (!sessionListRef.value || !sessionState.hasMore) return

  const { scrollHeight, clientHeight } = sessionListRef.value
  if (scrollHeight <= clientHeight) {
    loadSessionList()
  }
}

// 触底加载处理
const handleScroll = () => {
  if (!sessionListRef.value || sessionState.loading || !sessionState.hasMore)
    return

  const { scrollTop, scrollHeight, clientHeight } = sessionListRef.value
  const threshold = 50

  if (scrollTop + clientHeight >= scrollHeight - threshold) {
    loadSessionList()
  }
}

// 选择会话
const onSelectSession = async (id: string) => {
  if (aiStore.currentSessionId === id) return

  aiStore.setCurrentSessionId('')
  aiStore.setCurrentSessionId(id)
  aiStore.setSelectedToolType(TOOL_TYPE.CHAT)
}

// 删除会话
const onDeleteSession = async (id: string) => {
  $SKConfirm.warning({
    title: 'Confirm delete chat history?',
    message: 'After deletion, the chat history will not be recoverable',
    confirmButtonText: 'Delete',
    cancelButtonText: 'Cancel',
    onConfirm: async () => {
      return new Promise<void>(async resolve => {
        try {
          if (aiStore.currentSessionId === id) {
            aiStore.setCurrentSessionId('')
          }
          const { success } = await deleteSessionApi({ session_id: id })
          if (success) {
            $SKToast.warning('Successful')
            loadSessionList(true)
            return resolve()
          } else {
            throw new Error('删除失败')
          }
        } catch (error) {
          $SKToast.warning('Delete failed, please try again later')
          return resolve()
        }
      })
    },
  })
}

// 生命周期钩子和监听器
onMounted(() => {
  nextTick(checkAndLoadMore)
})

watch(sessionVisiable, (newVal: string) => {
  if (newVal === 'History') {
    nextTick(checkAndLoadMore)
  }
})

defineExpose({
  loadSessionList,
})
</script>

<style lang="scss" scoped>
.ai-sider-container {
  width: 300px;
  height: 100vh;
  padding: 0 16px 24px 16px;
  box-sizing: border-box;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
  box-sizing: border-box;
  border-right: 1px solid #eeeeee;

  .ai-logo {
    display: flex;
    user-select: none;
    margin-top: 19.5px;

    .logo {
      width: 35px;
      margin-right: 18.5px;
    }

    .title {
      font-size: 24px;
      font-weight: 500;
      line-height: normal;
      color: #171a23;
    }
  }

  .chat-type-list {
    padding: 30px 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 6px;

    .type-item {
      display: flex;
      align-items: center;
      gap: 14px;
      width: 100%;
      height: 48px;
      padding: 0 18px;
      user-select: none;
      border-radius: 8px;
      box-sizing: border-box;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: #ffffff;
        // transform: translateY(-2px);
        // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

        img {
          transform: scale(1.1);
        }
      }

      &:active {
        transform: translateY(0);
        // box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      }

      img {
        width: 20px;
        height: 20px;
        transition: transform 0.3s ease;
      }

      span {
        font-size: 16px;
        color: #171a23;
        transition: color 0.3s ease;
      }

      &.new-chat {
        font-weight: 500;
        background: #f1f3f6;
      }

      &.active {
        background: #ffffff;
      }
    }
  }

  :deep(.el-collapse) {
    .el-collapse-item,
    .el-collapse-item__header,
    .el-collapse-item__wrap {
      background-color: transparent;
    }

    .el-collapse-item__header {
      margin: 0;
      padding: 0 8px;
      font-size: 16px;
      color: #6c6f76;
      display: flex;
      justify-content: space-between;
      align-items: center;
      user-select: none;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        color: #171a23;
      }
    }

    .session-list {
      flex: 1;
      overflow-y: auto;
      height: calc(100vh - 500px);
      padding-right: 5px;
      box-sizing: border-box;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        border-radius: 2px;
        background-color: #f5f5f5;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 2px;
        background-color: #e0e0e0;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #bdbdbd;
        }
      }

      .session-list-status {
        text-align: center;
        padding: 10px 0;
        font-size: 12px;
        color: #c0c4cc;
      }

      .session-item {
        width: 100%;
        margin-bottom: 6px;
        height: 40px;
        line-height: 40px;
        border-radius: 6px;
        padding: 0 30px 0 8px;
        font-size: 14px;
        color: #6c6f76;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-sizing: border-box;
        position: relative;
        user-select: none;
        cursor: pointer;

        &.active {
          color: #171a23;
          background: #ffffff;
        }

        &:hover {
          color: #171a23;
          background: #ffffff;

          .delete {
            opacity: 1;
            transform: translateY(-50%) translateX(0);
          }
        }

        .delete {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%) translateX(10px);
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          background: rgba(0, 0, 0, 0.05);
          opacity: 0;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;

          &:hover {
            background: rgba(0, 0, 0, 0.1);

            img {
              transform: scale(1.1);
            }
          }

          img {
            width: 14px;
            height: 14px;
            transition: transform 0.3s ease;
          }
        }
      }
    }
  }

  .capacity-block {
    margin-top: auto;
    display: flex;
    flex-direction: column;

    .header-line {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text {
        font-weight: 400;
        font-size: 12px;
        font-variation-settings: 'opsz' auto;
        color: #171a23;
      }

      .img {
        height: 45px;
        cursor: pointer;
      }
    }

    // .storage-progress {
    // }
  }
}
</style>
