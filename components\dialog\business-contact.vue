<template>
  <div class="p-business-contact-dialog">
    <ElIconCloseBold class="close-icon" @click="close" />
    <div class="title-container">请确认您的联系方式</div>
    <div class="form-container">
      <div v-for="item of formInfo" :key="item.key" class="form-item">
        <div class="label-text">
          <span v-if="item.required" class="red-text">*</span>{{ item.label }}:
        </div>
        <div class="input-container">
          <input
            v-model="formData[item.key]"
            class="input-el"
            placeholder="Enter your text"
          />
        </div>
      </div>
      <div class="description-input-container">
        <div class="description-input-wrapper">
          <textarea
            v-model="formData.description"
            class="textarea-el"
            rows="3"
            :maxlength="200"
            placeholder="可输入您的需求，限200字符（不是必填项）"
          ></textarea>
        </div>
      </div>
    </div>
    <div class="button-container">
      <div class="button-item cancel-button" @click="cancel">No</div>
      <div class="button-item confirm-button" @click="submit">Yes</div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(['close'])

const defaultData = {
  name: '',
  phone: '',
  email: '',
  company: '',
  description: '',
}
const formInfo = ref([
  {
    label: '姓名',
    key: 'name',
    required: true,
  },
  {
    label: '电话',
    key: 'phone',
    required: false,
  },
  {
    label: '邮箱',
    key: 'email',
    required: true,
  },
  {
    label: '公司名称',
    key: 'company',
    required: true,
  },
])
const formData: Ref<{
  [propName: string]: string;
}> = ref({ ...defaultData })
const close = () => {
  emit('close')
}
const cancel = () => {
  close()
  formData.value = { ...defaultData }
}
// TODO:
const submit = () => {
  close()
}
</script>

<style lang="scss" scoped>
.p-business-contact-dialog {
  position: relative;
  background-color: #ffffff;
  .close-icon {
    position: absolute;
    right: 19px;
    top: 16px;
    width: 18px;
    height: 18px;
    color: #666666;
    cursor: pointer;
  }
  .title-container {
    color: #666666;
    // font-family: 'Poppins', sans-serif;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    padding: 40px 0 20px;
  }
  .form-container {
    .form-item {
      padding: 0 32px 0 34px;
      display: flex;
      align-items: center;
      margin-top: 12px;
      &:first-child {
        margin-top: 0;
      }
      .label-text {
        width: 94px;
        flex-shrink: 0;
        color: #333333;
        // font-family: 'Segoe UI', sans-serif;
        font-size: 14px;
        .red-text {
          color: #fa5151;
          // font-family: 'Segoe UI', sans-serif;
          font-size: 14px;
        }
      }
      .input-container {
        height: 32px;
        border-radius: 8px;
        background-color: #eeeeee;
        flex: 1;
        padding: 0 8px;
        display: flex;
        align-items: center;
        .input-el {
          width: 100%;
          color: #666968;
          // font-family: 'Poppins', sans-serif;
          font-size: 13px;
          &::placeholder {
            color: #666968;
            // font-family: 'Poppins', sans-serif;
            font-size: 13px;
          }
        }
      }
    }
    .description-input-container {
      padding: 0 32px;
      margin-top: 20px;
      .description-input-wrapper {
        border-radius: 8px;
        background: #eeeeee;
        padding: 10px 0;
        .textarea-el {
          padding: 0 22px;
          width: 100%;
          color: #333333;
          // font-family: 'Segoe UI', sans-serif;
          font-size: 13px;
          box-sizing: border-box;
          &::placeholder {
            color: #999999;
            // font-family: 'Segoe UI', sans-serif;
            font-size: 13px;
          }
        }
      }
    }
  }
  .button-container {
    padding: 30px;
    display: flex;
    .button-item {
      @include flex-all-center;
      width: 246px;
      height: 41px;
      border-radius: 8px;
      cursor: pointer;
      &:first-child {
        margin-right: 12px;
      }
    }
    .cancel-button {
      color: #343434;
      // font-family: 'Poppins', sans-serif;
      font-size: 16px;
      background: #e4e4e4;
    }
    .confirm-button {
      background: linear-gradient(270deg, #fa5151 0.01%, #f18d02 99.99%);
      color: #ffffff;
      // font-family: 'Poppins', sans-serif;
      font-size: 16px;
      font-weight: 600;
    }
  }
}
</style>
