<template>
  <div class="dialog-content">
    <img
      src="@/assets/img/auth/register/close.svg"
      class="close-btn"
      alt="Close"
      @click="closeDialog"
    />
    <div class="dialog-title">Welcome to Plasdata AI</div>
    <div class="description">
      Complete required info for a better product experience
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <div class="name-input-wrapper">
        <el-form-item label="Personal Details" prop="first_name">
          <el-input v-model="form.first_name" placeholder="First Name" />
        </el-form-item>
        <el-form-item prop="last_name">
          <el-input v-model="form.last_name" placeholder="Last Name" />
        </el-form-item>
      </div>

      <el-form-item label="Company of employment" prop="company_name">
        <el-input
          v-model="form.company_name"
          placeholder="Enter Company Name"
        />
      </el-form-item>

      <el-form-item label="Company Type" prop="company_type">
        <el-select
          v-model="form.company_type"
          :teleported="false"
          placeholder="Select type"
        >
          <el-option
            v-for="item in companyTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="Occupation" prop="occupation">
        <el-select
          v-model="form.occupation"
          :teleported="false"
          placeholder="Select occupation"
        >
          <el-option
            v-for="item in occupationOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <div class="create" @click="submit">Done</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'

const emit = defineEmits<{
  (e: 'close'): void;
}>()

const { $SKToast } = useNuxtApp()

const formRef = ref<FormInstance>()
const form = ref({
  first_name: '',
  last_name: '',
  company_name: '',
  company_type: '',
  occupation: '',
})
const rules = {
  first_name: [{ required: true, message: 'Please fill in first name' }],
  last_name: [{ required: true, message: 'Please fill in last name' }],
  company_name: [{ required: true, message: 'Please fill in company name' }],
  company_type: [{ required: true, message: 'Please fill in company type' }],
  occupation: [{ required: true, message: 'Please fill in occupation' }],
}

const companyTypeOptions = [
  { label: 'Private', value: 'private' },
  { label: 'Public', value: 'public' },
  { label: 'Government', value: 'government' },
  { label: 'Non-profit', value: 'non-profit' },
  { label: 'Other', value: 'other' },
]

const occupationOptions = [
  { label: 'Software Engineer', value: 'software_engineer' },
  { label: 'Product Manager', value: 'product_manager' },
  { label: 'Designer', value: 'designer' },
  { label: 'Other', value: 'other' },
]

const closeDialog = () => {
  emit('close')
}

const submit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) return
    closeDialog()
    $SKToast.warning('Submission Successful')
  })
}
</script>

<style scoped lang="scss">
.dialog-content {
  width: 560px;
  border-radius: 16px;
  padding: 32px;
  box-sizing: border-box;
  background: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;

  .close-btn {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
  }

  .dialog-title {
    font-size: 24px;
    font-weight: bold;
    color: #171a23;
    margin-bottom: 5px;
  }
  .description {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 24px;
  }

  .name-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 16px;
  }

  .el-input {
    width: 100%;
  }

  .el-form-item {
    width: 100%;

    margin-bottom: 20px;
  }
  :deep(.el-form-item__label) {
    font-size: 14px;
    color: #171a23;
  }

  :deep(.el-input__wrapper),
  :deep(.el-select__wrapper) {
    height: 40px;
    border-radius: 8px;
    border: none;
    box-shadow: none;
    background: #f7f8fc;
  }
  .el-input,
  .el-select {
    width: 100%;
    border-radius: 8px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    &:hover,
    &:focus,
    &:focus-within {
      border: 1px solid #d9d9d9;
    }
  }
  :deep(.el-input__inner) {
    &::placeholder {
      color: #9295ab;
      font-family: normal;
    }
  }
  :deep(.el-select__placeholder) {
    font-family: normal;
    font-size: 16px;
    color: #333333;
    &.is-transparent {
      color: #9295ab;
    }
  }

  .cover-upload {
    width: 60px;
    height: 60px;
  }
  .cover-upload-trigger {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    user-select: none;
    cursor: pointer;
    position: relative;
    &:hover {
      .mask {
        opacity: 1;
      }
    }
    img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 8px;
      opacity: 0;
      transition: all 0.3s ease;
      z-index: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    gap: 16px;
    .create {
      width: 160px;
      height: 40px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;

      font-size: 14px;
      font-weight: bold;
      color: #6c6f76;
      user-select: none;
      cursor: pointer;
      transition: all 0.3s ease;

      color: #ffffff;
      background: #ff6600;
      &:hover {
        background: lighten($color: #ff6600, $amount: 10);
      }
      &:active {
        background: darken($color: #ff6600, $amount: 10);
      }
    }
  }
}
</style>
