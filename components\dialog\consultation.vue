<template>
  <div
    class="c-consultation-dialog"
    :class="{ 'consultation-dialog--success': isSuccess }"
  >
    <el-icon
      v-if="!isSuccess"
      class="close-icon"
      :size="34"
      color="#000"
      @click="close"
      ><Close
    /></el-icon>
    <div v-if="!isSuccess">
      <div class="title-text">Contact us</div>
      <div class="sub-text">
        We take every inquiry seriously and aim to respond within 24 hours.
      </div>
      <div class="form-container">
        <div class="form-item">
          <div class="label-container">
            <span class="label-text">Name</span>
            <span class="red-text">*</span>
          </div>
          <el-input v-model="formData.name" class="input-el"></el-input>
        </div>
        <div class="form-item">
          <div class="label-container">
            <span class="label-text">Email address</span>
            <span class="red-text">*</span>
          </div>
          <el-input v-model="formData.email" class="input-el"></el-input>
        </div>
        <div class="form-item">
          <div class="label-container">
            <span class="label-text">Contents of consultation</span>
            <span class="red-text">*</span>
          </div>
          <el-input
            v-model="formData.question"
            class="textarea-el"
            type="textarea"
            :rows="4"
          ></el-input>
        </div>
      </div>
      <div class="submit-button" @click="submit">
        <span class="submit-text">
          <el-icon v-if="loading" class="is-loading"> <Loading /> </el-icon>
          Welcome to submit
        </span>
      </div>
    </div>
    <div v-show="isSuccess" class="success-container">
      <img class="success-icon" src="@/assets/img/common/success.svg" />
      <span class="success-text">Submitted</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Close, Loading } from '@element-plus/icons-vue'
import { userHelpRequest } from '@/api/user'
import { useUserStore } from '@/store/user'

const emit = defineEmits(['close'])
const userStore = useUserStore()
const formData = reactive({
  question: '',
  name: '',
  email: '',
})
const loading = ref(false)
const isSuccess = ref(false)

onMounted(() => {
  const userInfo = userStore.info
  formData.name = userInfo.name || ''
  formData.email = userInfo.email || ''
})
const submit = async () => {
  if (loading.value) return
  if (!formData.question.trim()) {
    ElMessage.error({
      message: 'Please enter your question',
      customClass: 'el-message-override',
    })
    return
  }
  if (!formData.name.trim()) {
    ElMessage.error({
      message: 'Please enter your name',
      customClass: 'el-message-override',
    })
    return
  }
  if (!isEmail(formData.email)) {
    ElMessage.error({
      message: 'Please enter valid email address',
      customClass: 'el-message-override',
    })
    return
  }
  loading.value = true
  const { success, message } = await userHelpRequest({
    content: formData.question,
    name: formData.name,
    email: formData.email,
    url: window.location.href,
  })
  loading.value = false
  isSuccess.value = success
  if (!success) {
    ElMessage.error(message)
    return
  }
  isSuccess.value = true
  setTimeout(() => {
    close()
  }, 2000)
}
const close = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
.c-consultation-dialog {
  width: 670px;
  padding: 48px;
  box-sizing: border-box;
  position: relative;
  .close-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 14px;
    cursor: pointer;
  }
  .success-container {
    @keyframes fade-in {
      0% {
        opacity: 0;
        transform: translate(0, -20px);
      }
      100% {
        transform: translate(0, 0);
        opacity: 1;
      }
    }
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: fade-in 0.3s;
    .success-icon {
      width: 90px;
      height: 90px;
    }
    .success-text {
      margin-top: 16px;
      // font-family: Source Han Sans, sans-serif;
      font-size: 18px;
      font-weight: normal;
      line-height: 24px;
      letter-spacing: 0px;
      color: #333333;
    }
  }
  .title-text {
    // font-family: Microsoft YaHei, sans-serif;
    font-size: 36px;
    font-weight: bold;
    line-height: normal;
    display: flex;
    align-items: center;
    letter-spacing: 0em;
    color: #333333;
  }

  .sub-text {
    margin-top: 10px;
    color: #999999;
  }

  .form-container {
    margin-top: 22px;
    .form-item {
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 0;
      }

      :deep(.el-input__wrapper) {
        background: #f9f9f9;
      }
      :deep(.el-input__inner) {
        &::placeholder {
          color: #86909c;
        }
      }
      :deep(.el-textarea__inner) {
        height: 116px !important;
        padding: 5px 3px 5px 10px;
        border-radius: 8px;
        resize: none;
        background: #f9f9f9;

        &::placeholder {
          color: #86909c;
        }

        &::-webkit-scrollbar {
          width: 2px;
        }
        &::-webkit-scrollbar-track {
          border-radius: 2px;
          background-color: #cccccc;
          margin: 8px 0;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 2px;
          background-color: #666666;
        }
      }

      .label-container {
        margin-bottom: 13px;
        .label-text {
          // font-family: Microsoft YaHei, sans-serif;
          font-size: 16px;
          font-weight: normal;
          line-height: 22px;
          letter-spacing: 0px;
          font-variation-settings: 'opsz' auto;
          color: #666666;
        }
        .red-text {
          // font-family: Microsoft YaHei, sans-serif;
          font-size: 16px;
          font-weight: normal;
          line-height: 22px;
          letter-spacing: 0px;
          font-variation-settings: 'opsz' auto;
          color: #ff0000;
        }
      }
    }
  }
  .submit-button {
    @include flex-all-center;
    margin-top: 48px;
    height: 56px;
    border-radius: 8px;
    background: #ff6600;
    cursor: pointer;
    user-select: none;
    .submit-text {
      user-select: none;
      // font-family: Microsoft YaHei, sans-serif;
      font-size: 22px;
      font-weight: normal;
      line-height: 31px;
      color: #ffffff;
      display: inline-block;
      position: relative;
      .is-loading {
        position: absolute;
        top: calc(50% - 11px);
        right: 100%;
        margin-right: 5px;
      }
    }
    transition: background 0.1s;
    &:hover {
      background: lighten(#ff6600, 5);
    }
    &:active {
      background: darken(#ff6600, 5);
    }
  }
}

.consultation-dialog--success {
  width: 192px;
  height: 192px;
  padding: 35px 0 27px;
}
</style>
