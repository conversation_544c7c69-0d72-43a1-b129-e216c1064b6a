<template>
  <div v-clickOutside:filter-drawer="closeDrawer" class="filter-drawer">
    <div class="drawer-header">
      <div class="title">
        Material Filter
        <template v-if="conditionCount">({{ conditionCount }})</template>
      </div>
      <img
        src="@/assets/img/auth/register/close.svg"
        class="close-btn"
        alt="Close"
        @click="closeDrawer"
      />
      <div class="description">Add or remove material information</div>
    </div>
    <SimpleBar class="filter-list" data-simplebar-auto-hide="true">
      <div
        v-for="item in conditionTypeList"
        :key="item.key"
        class="filter-type"
      >
        <div class="type-label">{{ item.value }}</div>
        <div class="type-value">
          <el-tag
            v-for="(tag, index) in filterData[item.key]"
            :key="index"
            class="filter-tag"
            closable
            :disable-transitions="false"
            size="large"
            @close="handleClose(item.key, tag)"
          >
            {{ tag.name }}
          </el-tag>
          <el-input
            v-if="currentKey === item.key && inputVisible"
            ref="inputRef"
            v-model="inputValue"
            class="input-new-tag"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          >
          </el-input>
          <div v-else class="button-new-tag" @click.stop="showInput(item.key)">
            <el-icon class="icon-plus"><Plus /></el-icon>
            <span>Add</span>
          </div>
        </div>
      </div>
    </SimpleBar>
    <div class="submit-btn" @click="submit">Confirm search</div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'FilterDrawer',
})
</script>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import SimpleBar from 'simplebar-vue'
import 'simplebar/dist/simplebar.min.css'

const CONDITION_TYPE = {
  grade: 'Grade',
  company: 'Company',
  category: 'Category',
  feature: 'Feature',
  filler: 'Filler',
  property: 'Property',
  usage: 'Usage',
}

const props = defineProps<{
  filterData: any;
}>()

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submit', filterMessage: string): void;
}>()

const currentKey = ref('')
const inputVisible = ref(false)
const inputValue = ref('')

const filterData = ref(
  Object.entries(props.filterData).reduce((acc: any, [key, value]: any) => {
    acc[key] = value?.filter((item: any) => item.is_calculate) || []
    return acc
  }, {}),
)

const conditionCount = computed(() => {
  return new Set(Object.values(filterData.value).flat()).size
})
const conditionTypeList = computed(() => {
  return Object.entries(CONDITION_TYPE).map(([key, value]) => ({
    key,
    value,
  }))
})

const closeDrawer = () => {
  emit('close')
}

const inputRef: Ref<HTMLInputElement[]> = ref([])
const showInput = (key: string) => {
  currentKey.value = key
  inputVisible.value = true
  nextTick(() => {
    inputRef.value[0]?.focus()
  })
}

const handleInputConfirm = () => {
  const value = inputValue.value.trim()
  if (value) {
    filterData.value[currentKey.value].push({ name: value })
  }
  inputVisible.value = false
  inputValue.value = ''
  currentKey.value = ''
}

const handleClose = (key: string, tag: any) => {
  filterData.value[key] = filterData.value[key].filter(
    (item: any) => item.name !== tag.name,
  )
}

const submit = () => {
  const data = Object.entries(filterData.value).reduce(
    (acc: any, [key, value]: any) => {
      if (value.length <= 0) {
        return acc
      }
      acc.count += value.length
      const conditionType = CONDITION_TYPE[key as keyof typeof CONDITION_TYPE]
      const conditionValue = value.map((item: any) => item.name).join('、')
      acc.message += `${conditionType}：${conditionValue}\n`
      return acc
    },
    {
      count: 0,
      message: '',
    },
  )
  if (data.count <= 0) {
    ElMessage({
      message: 'Please fill in at least one filter condition',
      type: 'error',
      duration: 1000,
      customClass: 'sk-toast',
    })
    return
  }
  const filterMessage = `Here are the material filtering details:\n${data.message}Provide relevant material recommendations based on the given filtering information.`
  emit('submit', filterMessage)
  emit('close')
}
</script>

<style lang="scss" scoped>
.filter-drawer {
  width: 350px;
  height: 100%;
  padding: 16px;
  background: #ffffff;
  border-radius: 12px;
  box-sizing: border-box;
  box-shadow: 0 3px 20px 0 rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;

  :deep(.simplebar-track) {
    right: -12px;
  }

  .drawer-header {
    position: relative;
    user-select: none;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #3d3d3d;
    }
    .close-btn {
      width: 16px;
      height: 16px;
      position: absolute;
      right: 0;
      top: 0;
      cursor: pointer;
      transition: filter 0.2s ease;
      &:hover {
        filter: brightness(0.8);
      }
    }
    .description {
      margin-top: 10px;
      font-size: 12px;
      color: #767676;
      user-select: none;
    }
  }

  .filter-list {
    height: calc(100% - 110px);
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: 10px 0;
    box-sizing: border-box;
    .filter-type {
      margin: 8px 0;
      .type-label {
        margin-bottom: 8px;
        font-size: 14px;
        color: #171a23;
      }
      .type-value {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;

        .filter-tag {
          border: none;
          background: #f7f8fa;
          user-select: none;
          transition: all 0.2s ease;
          flex-shrink: 0;
          flex-grow: 0;
          &:hover {
            background: darken(#f7f8fa, 3%);
          }
        }
        :deep(.el-tag) {
          font-size: 12px;
          color: #6c6f76;
          margin: 0;
        }
        :deep(.el-tag__close) {
          font-size: 14px;
          color: #6c6f76;
          margin-top: 1px;
          &:hover {
            background: darken(#f7f8fa, 10%);
          }
        }
        .button-new-tag {
          width: 80px;
          height: 32px;
          border-radius: 4px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          padding: 0 12px;
          gap: 8px;
          background: #ffffff;
          box-sizing: border-box;
          border: 1px dashed #aaaaaa;
          font-size: 12px;
          color: #6c6f76;
          cursor: pointer;
          transition: all 0.2s ease;
          flex-shrink: 0;
          &:hover {
            background: darken(#ffffff, 5%);
          }
          .icon-plus {
            font-size: 14px;
          }
        }
        .input-new-tag {
          height: 32px;
          width: fit-content;
          flex-shrink: 0;
        }
        :deep(.el-input__inner) {
          height: 32px;
          padding: 0 8px;
          min-width: 80px;
          max-width: 308px;
          font-size: 14px;
        }
        :deep(.el-input__wrapper) {
          height: 32px;
          padding: 0;
          min-width: 80px;
          max-width: 308px;
        }
      }
    }
  }

  .submit-btn {
    width: 100%;
    height: 40px;
    margin-top: auto;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ff7700;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    flex-shrink: 0;
    user-select: none;
    cursor: pointer;
    transition: all 0.2s ease;
    &:hover {
      background: lighten(#ff7700, 5%);
    }
    &:active {
      background: darken(#ff7700, 5%);
    }
  }

  :deep(.simplebar-scrollbar) {
    &::before {
      background: #333333 !important;
      width: 5px;
    }
  }
  :deep(.simplebar-track) {
    right: -15px;
  }
}
</style>
<style lang="scss">
.sk-toast {
  top: 35vh !important;
  min-width: auto;
  width: fit-content;
  height: 56px;
  border-radius: 6px;
  background: #3d3d3d;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;

  .el-message__icon {
    display: none;
  }
  .el-message__content {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    color: #ffffff;
  }
}
</style>
