<template>
  <transition name="message-box-fade">
    <div v-if="visible" class="message-box-mask" @click.self="handleMaskClick">
      <div class="message-box-container" :class="[`message-box-${type}`]">
        <img
          v-if="showClose"
          src="@/assets/img/auth/register/close.svg"
          class="message-box-close"
          alt="Close"
          @click="handleClose"
        />
        <div class="message-box-header">
          <div class="message-box-title">
            <div
              v-if="showIcon"
              class="message-box-icon-circle"
              :style="{ backgroundColor: TypeColor[type] }"
            >
              <img
                v-if="iconType === 'error'"
                src="@/assets/img/icon/toast-icon-1.svg"
              />
              <img v-else src="@/assets/img/icon/toast-icon-2.svg" />
            </div>
            <span class="message-box-title-text">{{ title }}</span>
          </div>
        </div>
        <div class="message-box-content">
          <p class="message-box-message">{{ content || message }}</p>
        </div>
        <div class="message-box-footer">
          <el-button
            v-if="showCancelButton"
            class="message-box-cancel"
            @click="handleCancel"
          >
            {{ cancelButtonText }}
          </el-button>
          <el-button
            class="message-box-confirm"
            :color="TypeColor[type]"
            :loading="loading"
            @click="handleConfirm"
          >
            {{ confirmButtonText }}
          </el-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref } from 'vue'

import { MessageType } from '~/constants/message-type'

const TypeColor: Record<MessageType, string> = {
  [MessageType.INFO]: '#409eff',
  [MessageType.SUCCESS]: '#67c23a',
  [MessageType.WARNING]: '#ff6600',
  [MessageType.ERROR]: '#ea0000',
}

const props = defineProps({
  title: {
    type: String,
    default: '提示',
  },
  message: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  type: {
    type: String as () => MessageType,
    default: MessageType.INFO,
  },
  iconType: {
    type: String as () => 'success' | 'error',
    default: 'error',
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  showCancelButton: {
    type: Boolean,
    default: true,
  },
  confirmButtonText: {
    type: String,
    default: '确定',
  },
  cancelButtonText: {
    type: String,
    default: '取消',
  },
  closeOnClickMask: {
    type: Boolean,
    default: true,
  },
  onConfirm: {
    type: Function,
    default: () => {},
  },
  onCancel: {
    type: Function,
    default: () => {},
  },
  onClose: {
    type: Function,
    default: () => {},
  },
})

const visible = ref(false)
const loading = ref(false)

const showIcon = computed(() => {
  return props.type !== undefined
})

const handleClose = async () => {
  if (typeof props.onClose === 'function') {
    await props.onClose()
  }
  close()
}

const handleConfirm = async () => {
  if (typeof props.onConfirm === 'function') {
    loading.value = true
    await props.onConfirm()
    loading.value = false
  }
  close()
}

const handleCancel = async () => {
  if (typeof props.onCancel === 'function') {
    await props.onCancel()
  }
  close()
}

const handleMaskClick = () => {
  if (props.closeOnClickMask) {
    handleClose()
  }
}

const open = () => {
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({
  open,
  close: handleClose,
})
</script>

<style lang="scss" scoped>
.message-box-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 15vh;
  z-index: 99999;
}

.message-box-container {
  width: 400px;
  padding: 24px 16px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 4px 16px 0px rgba(173, 190, 215, 0.25);
  position: relative;

  .message-box-close {
    position: absolute;
    right: 12px;
    top: 12px;
    width: 13px;
    height: 13px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    &:hover {
      filter: brightness(0);
    }
  }
}

.message-box-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.message-box-title {
  display: flex;
  align-items: center;
}

.message-box-title-text {
  font-size: 18px;
  font-weight: normal;
  line-height: normal;
  color: #252c58;
  text-shadow: 0px 4px 16px rgba(173, 190, 215, 0.25);
  user-select: none;
}
.message-box-icon-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 90%;
    height: 90%;
  }
}

.message-box-content {
  padding: 20px;
  font-size: 14px;
  line-height: 1.5;
}

.message-box-message {
  margin: 0;
  word-break: break-word;

  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  color: #9295ab;
}

.message-box-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;

  .message-box-cancel {
    border: 1px solid #d9d9d9;
    color: #6c6f76;
  }
  .message-box-confirm {
    color: #ffffff;
  }
}

.message-box-fade-enter-active,
.message-box-fade-leave-active {
  transition: all 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.message-box-fade-enter-from,
.message-box-fade-leave-to {
  opacity: 0;
}

.message-box-fade-enter-from .message-box-container {
  transform: translateY(-20px);
}

.message-box-fade-leave-to .message-box-container {
  transform: translateY(-20px);
}

.message-box-container {
  transition: transform 0.25s cubic-bezier(0.645, 0.045, 0.355, 1);
}
</style>
