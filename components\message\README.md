# Message 消息组件

消息组件用于向用户提供反馈信息，包括成功、警告、错误等状态。

## 使用方式

### Toast 消息提示

```js
// 基本使用
$SKToast.info('这是一条消息')
$SKToast.success('操作成功')
$SKToast.warning('警告信息')
$SKToast.error('错误信息')

// 自定义配置
$SKToast.info('消息内容', {
  duration: 5000, // 显示时长，单位毫秒
  showClose: true, // 是否显示关闭按钮
  centered: true, // 是否垂直居中显示
})

// 通用方法
$SKToast.show({
  message: '消息内容',
  type: 'success',
  duration: 5000,
  showClose: true,
  centered: true, // 设置为true时将在屏幕中央显示
})
```

### MessageBox 确认对话框

```js
// 基本使用
const result = await $SKConfirm.info({
  title: '提示',
  content: '确认执行此操作吗？',
})
if (result) {
  // 用户点击了确认
} else {
  // 用户点击了取消
}

// 不同类型
$SKConfirm.success({ content: '操作成功' })
$SKConfirm.warning({ content: '确认删除？' })
$SKConfirm.error({ content: '操作失败' })

// 自定义配置
$SKConfirm.info({
  title: '自定义标题',
  content: '自定义内容',
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  showCancelButton: true,
})

// 只有确认按钮的对话框
$SKConfirm.alert({
  content: '操作已完成',
})
```

## API

### Toast 配置项

| 参数      | 说明                                        | 类型     | 默认值 |
| --------- | ------------------------------------------- | -------- | ------ |
| type      | 类型，可选值：info、success、warning、error | string   | info   |
| message   | 消息文本                                    | string   | -      |
| duration  | 显示时间，单位为毫秒，设为 0 则不会自动关闭 | number   | 3000   |
| showClose | 是否显示关闭按钮                            | boolean  | false  |
| centered  | 是否垂直居中显示                            | boolean  | false  |
| onClose   | 关闭时的回调函数                            | Function | -      |

### MessageBox 配置项

| 参数              | 说明                                        | 类型     | 默认值 |
| ----------------- | ------------------------------------------- | -------- | ------ |
| type              | 类型，可选值：info、success、warning、error | string   | info   |
| title             | 标题                                        | string   | '提示' |
| content           | 消息内容                                    | string   | -      |
| message           | 消息内容（content 优先）                    | string   | -      |
| showCancelButton  | 是否显示取消按钮                            | boolean  | true   |
| confirmButtonText | 确认按钮文本                                | string   | '确定' |
| cancelButtonText  | 取消按钮文本                                | string   | '取消' |
| showClose         | 是否显示右上角关闭按钮                      | boolean  | true   |
| onConfirm         | 点击确认按钮时的回调函数                    | Function | -      |
| onCancel          | 点击取消按钮时的回调函数                    | Function | -      |
| onClose           | 关闭时的回调函数                            | Function | -      |
