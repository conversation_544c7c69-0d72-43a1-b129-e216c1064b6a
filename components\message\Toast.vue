<template>
  <transition name="toast-fade">
    <div
      v-show="visible"
      class="toast-container"
      :class="{ 'toast-centered': centered }"
      :style="centered ? {} : { top: `${offset}px` }"
    >
      <div class="toast-content">
        <div
          v-if="showIcon"
          class="toast-icon-circle"
          :class="[`toast-icon-circle-${type}`]"
        >
          <img
            v-if="iconType === 'error'"
            src="@/assets/img/icon/toast-icon-1.svg"
          />
          <img v-else src="@/assets/img/icon/toast-icon-2.svg" />
        </div>
        <span class="toast-message">{{ message }}</span>
      </div>
      <i v-if="showClose" class="toast-close" @click="close">×</i>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from 'vue'

import { MessageType } from '~/constants/message-type'

const props = defineProps({
  message: {
    type: String,
    required: true,
  },
  type: {
    type: String as () => MessageType,
    default: MessageType.INFO,
  },
  iconType: {
    type: String as () => 'success' | 'error',
    default: 'error',
  },
  duration: {
    type: Number,
    default: 3000,
  },
  showClose: {
    type: Boolean,
    default: false,
  },
  offset: {
    type: Number,
    default: 20,
  },
  centered: {
    type: Boolean,
    default: false,
  },
  onClose: {
    type: Function,
    default: () => {},
  },
})

const visible = ref(false)
const timer = ref<NodeJS.Timeout | null>(null)

const showIcon = computed(() => {
  return props.type !== undefined
})

const close = () => {
  visible.value = false

  setTimeout(() => {
    if (timer.value) {
      clearTimeout(timer.value)
      timer.value = null
    }
    props.onClose()
  }, 300)
}

const startTimer = () => {
  if (props.duration > 0) {
    timer.value = setTimeout(() => {
      close()
    }, props.duration)
  }
}

onMounted(() => {
  nextTick(() => {
    visible.value = true
    startTimer()
  })
})

defineExpose({
  close,
})
</script>

<style lang="scss" scoped>
.toast-container {
  position: fixed;
  left: 50%;
  top: 80px;
  transform: translateX(-50%);
  max-width: 500px;
  padding: 14px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  background-color: #fff;
  z-index: 9999999;
  box-shadow: 0px 4px 16px 0px rgba(163, 179, 204, 0.25);
  transition: opacity 0.3s, transform 0.3s, top 0.3s;

  &.toast-centered {
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.toast-content {
  display: flex;
  align-items: center;
  user-select: none;
}

.toast-icon-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  img {
    width: 90%;
    height: 90%;
  }

  &.toast-icon-circle-info {
    background-color: #409eff;
  }

  &.toast-icon-circle-success {
    background-color: #67c23a;
  }

  &.toast-icon-circle-warning {
    background-color: #ff6600;
  }

  &.toast-icon-circle-error {
    background-color: #ea0000;
  }
}

.toast-message {
  font-size: 14px;
  font-weight: normal;
  color: #171a23;
  word-break: break-word;
  white-space: pre-wrap;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.toast-close {
  margin-left: 16px;
  font-size: 18px;
  color: #909399;
  cursor: pointer;
  &:hover {
    color: #606266;
  }
}

.toast-fade-enter-active,
.toast-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
}

.toast-fade-enter-from {
  opacity: 0;
  transform: translate(-50%, -20px);
}

.toast-centered.toast-fade-enter-from {
  opacity: 0;
  transform: translate(-50%, -60%);
}

.toast-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -10px);
}

.toast-centered.toast-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -40%);
}
</style>
