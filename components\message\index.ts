import type { App, VNode } from 'vue'
import { createVNode, render } from 'vue'
import MessageBox from '@/components/message/MessageBox.vue'
import Toast from '@/components/message/Toast.vue'
import type {
  MessageBoxOptions,
  MessageOptions,
} from '@/constants/message-type.ts'
import { MessageType } from '@/constants/message-type.ts'

// 存储当前显示的所有Toast实例
const toastInstances: VNode[] = []
// 管理Toast的垂直偏移量
let toastOffset = 80
const toastGap = 16

// 创建并显示Toast
const showToast = (options: MessageOptions | string) => {
  // 如果传入的是字符串，则将其转换为对象
  const props = typeof options === 'string' ? { message: options } : options

  // 创建容器
  const container = document.createElement('div')

  // 计算偏移量
  const offset = toastOffset
  // 如果不是居中显示，则增加偏移量
  if (!props.centered) {
    toastOffset += 60 + toastGap // 每个Toast的高度 + 间距
  }

  // 创建VNode
  const vnode = createVNode(Toast, {
    ...props,
    offset,
    onClose: () => {
      // 从实例列表中移除
      const index = toastInstances.indexOf(vnode)
      if (index !== -1) {
        toastInstances.splice(index, 1)
      }

      // 延迟移除DOM，确保动画完成
      setTimeout(() => {
        // 关闭时移除DOM
        render(null, container)
        container.parentNode?.removeChild(container)

        // 调整其他Toast的位置
        if (!props.centered) {
          adjustToastPositions()
        }

        // 执行用户传入的onClose回调
        if (typeof props.onClose === 'function') {
          props.onClose()
        }
      }, 250) // 动画持续时间
    },
  })

  // 渲染并添加到DOM
  render(vnode, container)
  const rootElement =
    document.getElementById('__nuxt') || document.documentElement
  rootElement.appendChild(container)

  // 添加到实例列表，仅当不是居中显示时
  if (!props.centered) {
    toastInstances.push(vnode)
  }

  return vnode
}

// 调整所有Toast的位置
const adjustToastPositions = () => {
  let currentOffset = 80
  toastInstances.forEach(vnode => {
    if (vnode.component && vnode.component.props) {
      vnode.component.props.offset = currentOffset
      currentOffset += 60 + toastGap
    }
  })
  toastOffset = currentOffset
}

// 创建并显示MessageBox
const showMessageBox = (options: MessageBoxOptions | string) => {
  return new Promise((resolve, reject) => {
    // 如果传入的是字符串，则将其转换为对象
    const props = typeof options === 'string' ? { message: options } : options

    // 创建容器
    const container = document.createElement('div')

    // 创建VNode
    const vnode = createVNode(MessageBox, {
      ...props,
      onConfirm: async () => {
        try {
          if (typeof props.onConfirm === 'function') {
            await props.onConfirm()
          }
          // 延迟移除DOM，确保动画完成
          setTimeout(() => {
            // 关闭时移除DOM
            render(null, container)
            container.parentNode?.removeChild(container)
            resolve(true)
          }, 250) // 动画持续时间
        } catch (error) {
          reject(error)
        }
      },
      onCancel: async () => {
        try {
          if (typeof props.onCancel === 'function') {
            await props.onCancel()
          }
          // 延迟移除DOM，确保动画完成
          setTimeout(() => {
            // 关闭时移除DOM
            render(null, container)
            container.parentNode?.removeChild(container)
            resolve(false)
          }, 250) // 动画持续时间
        } catch (error) {
          reject(error)
        }
      },
      onClose: async () => {
        try {
          if (typeof props.onClose === 'function') {
            await props.onClose()
          }
          // 延迟移除DOM，确保动画完成
          setTimeout(() => {
            // 关闭时移除DOM
            render(null, container)
            container.parentNode?.removeChild(container)
            reject(new Error('MessageBox was closed'))
          }, 250) // 动画持续时间
        } catch (error) {
          reject(error)
        }
      },
    })

    // 渲染并添加到DOM
    render(vnode, container)
    const rootElement =
      document.getElementById('__nuxt') || document.documentElement
    rootElement.appendChild(container)

    // 显示MessageBox
    setTimeout(() => {
      if (vnode.component && vnode.component.exposed) {
        vnode.component.exposed.open()
      }
    })
  })
}

// 创建Toast便捷方法
const SKToast = {
  info: (message: string, options?: Partial<MessageOptions>) => {
    return showToast({ ...options, message, type: MessageType.INFO })
  },
  success: (message: string, options?: Partial<MessageOptions>) => {
    return showToast({ ...options, message, type: MessageType.SUCCESS })
  },
  warning: (message: string, options?: Partial<MessageOptions>) => {
    return showToast({ ...options, message, type: MessageType.WARNING })
  },
  error: (message: string, options?: Partial<MessageOptions>) => {
    return showToast({ ...options, message, type: MessageType.ERROR })
  },
  // 通用方法
  show: showToast,
}

// 创建MessageBox便捷方法
const SKConfirm = {
  info: (options: Partial<MessageBoxOptions>) => {
    return showMessageBox({ ...options, type: MessageType.INFO })
  },
  success: (options: Partial<MessageBoxOptions>) => {
    return showMessageBox({ ...options, type: MessageType.SUCCESS })
  },
  warning: (options: Partial<MessageBoxOptions>) => {
    return showMessageBox({ ...options, type: MessageType.WARNING })
  },
  error: (options: Partial<MessageBoxOptions>) => {
    return showMessageBox({ ...options, type: MessageType.ERROR })
  },
  alert: (options: Partial<MessageBoxOptions>) => {
    return showMessageBox({
      ...options,
      showCancelButton: false,
      type: MessageType.INFO,
    })
  },
  // 通用方法
  show: showMessageBox,
}

// 导出便捷方法
export { SKToast, SKConfirm, MessageType }

// 默认导出空对象，不再注册全局属性
export default {
  install: (app: App) => {
    // 不再注册全局属性，由 Nuxt 插件处理
  },
}
