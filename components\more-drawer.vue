<template>
  <div v-clickOutside:more-drawer="closeDrawer" class="more-drawer">
    <div class="drawer-header">
      <div class="title">More ({{ moreCount }})</div>
      <img
        src="@/assets/img/auth/register/close.svg"
        class="close-btn"
        alt="Close"
        @click="closeDrawer"
      />
    </div>
    <SimpleBar
      ref="productListRef"
      v-loading="loading && !isLoadMore"
      class="product-list"
      data-simplebar-auto-hide="true"
    >
      <div v-for="(item, index) in dataList" :key="index" class="product-item">
        <a class="title" :href="`/datasheet/${item.id}`" target="_blank">
          {{ item.title }}
        </a>
        <div class="company">{{ item.company }} · {{ item.category }}</div>
        <div v-if="item.feature" class="feature">
          <div class="label">Features:</div>
          <div class="value">{{ item.feature }}</div>
        </div>
        <div v-if="item.usage" class="usage">
          <div class="label">Application:</div>
          <div class="value">{{ item.usage }}</div>
        </div>
        <!-- <div class="interaction-block">
          <div class="item" @click="handleReplace(item)">
            <img src="@/assets/img/product/replace-icon.svg" />
            <span> Find alternatives </span>
          </div>
          <div
            class="item"
            :class="{ 'is-compare': item.isCompare }"
            @click="handleCompare(item)"
          >
            <img src="@/assets/img/product/comparison-icon.svg" />
            <span>
              {{ item.isCompare ? 'Already in comparison' : 'Add to comparison' }}
            </span>
          </div>
          <div class="item" @click="handleSupply(item)">
            <img src="@/assets/img/product/supply-icon.svg" />
            <span> Find supply </span>
          </div>
        </div> -->
      </div>

      <el-divider class="bottom-loading">
        <i v-if="loading && isLoadMore" class="el-icon-loading"></i>
        {{ loadMoreText }}
      </el-divider>
    </SimpleBar>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'MoreDrawer',
})
</script>

<script setup lang="ts">
import SimpleBar from 'simplebar-vue'
import { getMoreNoteApi } from '@/api/ai'
import type { MaterialData } from '@/types/product'
import 'simplebar/dist/simplebar.min.css'

const props = defineProps<{
  moreCount: number;
  moreKey: string;
}>()

const emit = defineEmits<{
  (e: 'close'): void;
  (e: 'submit', filterMessage: string): void;
}>()

const loading = ref(false)
const productList = ref<MaterialData[]>([])
const pageNo = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoadMore = ref(false)

const loadMoreText = computed(() => {
  if (loading.value && isLoadMore.value) {
    return 'Loading...'
  }
  if (productList.value.length >= 50) {
    return 'Show only the first 50 items'
  }
  if (productList.value.length >= 5) {
    return 'No more'
  }
  return ''
})

const dataList = computed(() => {
  return productList.value.map((item: any) => ({
    ...item,
    feature: item.feature.join(' / '),
    usage: item.usage.join(' / '),
  }))
})

const closeDrawer = () => {
  emit('close')
}

const loadData = async (isLoad = false) => {
  if (!isLoad) {
    pageNo.value = 1
    hasMore.value = true
    productList.value = []
  }

  if (!hasMore.value || loading.value) return

  isLoadMore.value = isLoad
  loading.value = true
  try {
    const {
      data: { list, is_end: isEnd },
    } = await getMoreNoteApi({
      cache_key: props.moreKey,
      page: pageNo.value,
      size: pageSize.value,
    })

    if (isLoad) {
      productList.value = [...productList.value, ...list]
    } else {
      productList.value = list
    }

    hasMore.value = !isEnd && productList.value.length < 50

    if (hasMore.value) {
      pageNo.value += 1
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
    isLoadMore.value = false
  }
}

watch(
  () => props.moreKey,
  (val: string | undefined) => {
    if (val) {
      loadData()
    }
  },
  { immediate: true },
)

const handleScroll = (container: HTMLElement) => {
  const scrollPosition = container.scrollTop + container.clientHeight
  const scrollHeight = container.scrollHeight
  const threshold = 150 // 提前150px触发加载

  if (
    scrollPosition >= scrollHeight - threshold &&
    hasMore.value &&
    !loading.value
  ) {
    loadData(true)
  }
}

const productListRef = ref<InstanceType<typeof SimpleBar> | null>(null)
onMounted(() => {
  nextTick(() => {
    if (productListRef.value) {
      const container = productListRef.value.$el.querySelector(
        '.simplebar-content-wrapper',
      ) as HTMLElement
      if (container) {
        container.addEventListener('scroll', () => handleScroll(container))
      }
    }
  })
})
</script>

<style lang="scss" scoped>
.more-drawer {
  width: 350px;
  height: 100%;
  padding: 16px;
  background: #ffffff;
  border-radius: 12px;
  box-sizing: border-box;
  box-shadow: 0 3px 20px 0 rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;

  :deep(.simplebar-track) {
    right: -12px;
  }

  .drawer-header {
    position: relative;
    user-select: none;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: 500;
      color: #3d3d3d;
    }
    .close-btn {
      width: 16px;
      height: 16px;
      position: absolute;
      right: 0;
      top: 0;
      cursor: pointer;
      transition: filter 0.2s ease;
      &:hover {
        filter: brightness(0.8);
      }
    }
  }

  .product-list {
    height: calc(100% - 37px);
    display: flex;
    flex-direction: column;

    .product-item {
      padding: 16px 0;
      min-height: 100px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      &:first-child {
        padding-top: 0;
      }
      &:not(:nth-last-child(2)) {
        border-bottom: 1px solid #e5e5e5;
      }

      .title {
        font-weight: 500;
        font-size: 16px;
        color: #171a23;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
      .company {
        font-size: 12px;
        font-weight: 350;
        color: #171a23;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .feature,
      .usage {
        display: flex;
        align-items: center;
        gap: 4px;
        .label {
          font-size: 12px;
          color: #171a23;
          flex-shrink: 0;
        }
        .value {
          font-size: 12px;
          color: #6c6f76;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .interaction-block {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
        .item {
          width: 92px;
          height: 32px;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-shrink: 0;
          background: #f1f3f6;
          gap: 8px;
          font-size: 12px;
          color: #171a23;
          user-select: none;
          cursor: pointer;
          transition: background 0.2s ease;
          &:hover {
            background: darken(#f1f3f6, 5%);
          }
          &:active {
            background: darken(#f1f3f6, 10%);
          }
          img {
            width: 14px;
          }
          span {
            font-size: 12px;
            font-weight: 350;
            color: #6c6f76;
          }
        }
        .is-compare {
          cursor: auto;
          filter: brightness(0.9);
          opacity: 0.6;
          pointer-events: none;
          letter-spacing: -0.03em;
          gap: 6px;
          &:hover,
          &:active {
            background: #f1f3f6;
          }
        }
      }
    }
  }

  :deep(.simplebar-scrollbar) {
    &::before {
      background: #333333 !important;
      width: 5px;
    }
  }
  :deep(.simplebar-track) {
    right: -15px;
  }

  .bottom-loading {
    margin: 10px 0 0 0;
    :deep(.el-divider__text) {
      font-size: 12px;
      color: #909399;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: max-content;
      margin-top: -2px;
    }
  }
}
</style>
