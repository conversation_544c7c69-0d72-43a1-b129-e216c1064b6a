<template>
  <div class="pagination-block">
    <div class="paging">
      <el-icon
        class="item"
        :class="{ disabled: !canPrevious }"
        @click="handlePrevious"
      >
        <ArrowLeft />
      </el-icon>
      <div>{{ page }}/{{ totalPage }}</div>
      <el-icon class="item" :class="{ disabled: !canNext }" @click="handleNext">
        <Right />
      </el-icon>
    </div>
    <div class="page">
      <div
        v-for="item in totalPage"
        :key="item"
        class="item"
        :class="{ active: item === page }"
        @click="changePage(item)"
      >
        {{ item }}
      </div>
    </div>
    <div class="size">
      <div
        v-for="item in limitList"
        :key="item.value"
        class="item"
        :class="{ active: item.active }"
        @click="changeLimit(item.value)"
      >
        {{ item.value }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, Right } from '@element-plus/icons-vue'

export type { PaginationLimit }

enum PaginationLimit {
  small = 10,
  medium = 25,
  large = 50,
}

const props = withDefaults(
  defineProps<{
    page: number;
    limit: number;
    total: number;
    limitOptions?: number[];
  }>(),
  {
    limitOptions: () => [
      PaginationLimit.small,
      PaginationLimit.medium,
      PaginationLimit.large,
    ],
  },
)

const emit = defineEmits<{
  (e: 'update:page', page: number): void;
  (e: 'update:limit', limit: number): void;
  (e: 'change'): void;
}>()

const totalPage = computed(() => {
  return Math.ceil(props.total / props.limit) || 1
})

const canPrevious = computed(() => {
  return props.page > 1
})

const canNext = computed(() => {
  return props.page < totalPage.value
})

const limitList = computed(() => {
  return props.limitOptions.map(item => {
    return {
      value: item,
      active: item === props.limit,
    }
  })
})

const handlePrevious = () => {
  if (canPrevious.value) {
    emit('update:page', props.page - 1)
    emit('change')
  }
}

const handleNext = () => {
  if (canNext.value) {
    emit('update:page', props.page + 1)
    emit('change')
  }
}

const changePage = (page: number) => {
  if (props.page === page) return
  emit('update:page', page)
  emit('change')
}

const changeLimit = (limit: number) => {
  emit('update:limit', limit)
  emit('update:page', 1)
  emit('change')
}
</script>

<style scoped lang="scss">
.pagination-block {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  background: #ffffff;
  .paging {
    .item:hover {
      background: #f8f9fa;
      box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    }
    .disabled {
      opacity: 0.5;
      cursor: not-allowed;
      &:hover {
        background: transparent;
        box-shadow: none;
      }
    }
  }
  .paging,
  .page,
  .size {
    padding: 3px;
    display: flex;
    align-items: center;
    height: 32px;
    border-radius: 8px;
    background: #f1f3f6;
    font-size: 14px;
    font-weight: 350;
    color: #3d3d3d;
    box-sizing: border-box;
    gap: 3px;
    .item {
      width: 32px;
      height: 100%;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
      cursor: pointer;
      transition: all 0.2s;
      &.active {
        background: #f8f9fa;
        box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
      }
      &:hover {
        background: #f8f9fa;
      }
      &:active {
        background: lighten($color: #f8f9fa, $amount: 10%);
      }
    }
  }
}
</style>
