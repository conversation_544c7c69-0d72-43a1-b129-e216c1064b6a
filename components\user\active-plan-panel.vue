<template>
  <div class="panel">
    <div class="panel-title">Active plan</div>
    <div class="panel-content">
      <div class="content-header">
        <div class="plan-type">{{ planType }}</div>
        <div class="plan-valid-until">Valid Until: {{ unlimited }}</div>
        <img
          class="upgrade-btn"
          src="@/assets/img/ai/ai-upgrade-img.svg"
          @click="handlePricingDialog"
        />
      </div>
      <div class="content-body">
        <div class="storage-capacity">
          <div class="storage-title">Storage Capacity</div>
          <div class="storage-value">
            <div class="storage-value-item">
              <div class="item-lable">Total Capacity:</div>
              <div class="item-value">{{ totalCapacity }}MB</div>
            </div>
            <div class="storage-value-item">
              <div class="item-lable">Used Capacity:</div>
              <div class="item-value">{{ usedCapacity }}MB</div>
            </div>
          </div>
          <div class="storage-progress">
            <el-progress
              :percentage="percentage"
              :show-text="false"
              color="#FF6600"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <aiPricingDialog
    v-model:visible="pricingDialogVisible"
    @enterprise="handleEnterpriseDialog"
  />
  <aiEnterpriseDialog v-model:visible="enterpriseDialogVisible" />
</template>

<script setup lang="ts">
import aiEnterpriseDialog from '@//components/ai-enterprise-dialog.vue'
import aiPricingDialog from '@//components/ai-pricing-dialog.vue'

const planType = ref('Free')
const unlimited = ref('Unlimited')
const totalCapacity = ref(100)
const usedCapacity = ref(65)
const percentage = computed(() => {
  return Math.round((usedCapacity.value / totalCapacity.value) * 100)
})

const pricingDialogVisible = ref(false)
const handlePricingDialog = () => {
  pricingDialogVisible.value = true
}

const enterpriseDialogVisible = ref(false)
const handleEnterpriseDialog = () => {
  enterpriseDialogVisible.value = true
}
</script>

<style scoped lang="scss">
.panel {
  width: 100%;
  padding: 25px 16px;
  .panel-title {
    margin-bottom: 8px;
    padding-bottom: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #171a23;
    border-bottom: 1px solid #f1f2f4;
  }
  .panel-content {
    .content-header {
      display: flex;
      flex-direction: column;
      position: relative;
      margin-top: 20px;
      gap: 5px;

      .plan-type {
        font-size: 24px;
        font-weight: 500;
        color: #171a23;
      }

      .plan-valid-until {
        font-size: 12px;
        font-weight: 350;
        color: #9295ab;
        margin-right: 10px;
      }

      .upgrade-btn {
        width: 95px;
        position: absolute;
        right: 20%;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }
    }
    .content-body {
      margin-top: 20px;
      .storage-capacity {
        width: 80%;
        border-radius: 8px;
        background: #ffffff;
        box-sizing: border-box;
        border: 1px solid #eeeeee;
        padding: 16px;
        .storage-title {
          font-size: 16px;
          font-weight: 500;
          color: #3d3d3d;
        }
        .storage-value {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #6c6f76;
          margin-top: 16px;
          gap: 36px;
          .storage-value-item {
            display: flex;
            align-items: center;
            justify-content: center;
            .item-lable {
              margin-right: 10px;
            }
          }
        }
        .storage-progress {
          margin-top: 12px;
        }
      }
    }
  }
}
</style>
