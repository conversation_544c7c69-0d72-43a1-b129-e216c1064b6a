<template>
  <el-dialog
    :model-value="props.visible"
    width="896px"
    :show-close="false"
    :append-to-body="true"
    @close="closeDialog"
  >
    <div class="dialog-content">
      <img
        src="@/assets/img/auth/register/close.svg"
        class="close-btn"
        alt="Close"
        @click="closeDialog"
      />
      <div class="dialog-left">
        <div class="dialog-title">Settings</div>
        <div class="settings-list">
          <div
            v-for="item in settingsList"
            :key="item.name"
            class="settings-item"
            :class="{ active: activePart.name === item.name }"
            @click="activePart = item"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="dialog-right">
        <component :is="activePart.component" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import activePlanPanel from '@//components/user/active-plan-panel.vue'
import ordersPanel from '@//components/user/orders-panel.vue'
import userInfoPanel from '@//components/user/userinfo-panel.vue'

const props = defineProps<{
  visible: boolean;
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>()

const closeDialog = () => {
  emit('update:visible', false)
}

const settingsList = [
  {
    name: 'User Information',
    component: markRaw(userInfoPanel),
  },
  {
    name: 'Active plan',
    component: markRaw(activePlanPanel),
  },
  {
    name: 'My Orders',
    component: markRaw(ordersPanel),
  },
]

const activePart = ref(settingsList[0])
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  padding: 0;
  background: transparent;
  .el-dialog__header {
    display: none;
  }
}
.dialog-content {
  width: 896px;
  height: 500px;
  border-radius: 16px;
  box-sizing: border-box;
  background: #ffffff;
  position: relative;
  display: flex;
  overflow: hidden;

  .close-btn {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
  }

  .dialog-left {
    width: 220px;
    height: 100%;
    padding: 20px 16px;
    background: #f7f8fa;
    flex-shrink: 0;
    .dialog-title {
      margin-bottom: 8px;
      padding-left: 8px;
      padding-bottom: 8px;
      font-size: 18px;
      font-weight: bold;
      color: #171a23;
    }
    .settings-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .settings-item {
        height: 40px;
        padding: 0 8px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        border-radius: 6px;
        cursor: pointer;
        user-select: none;
        font-size: 14px;
        line-height: 16px;
        color: #171a23;
        transition: background 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);
        &.active {
          background: #ffffff;
          font-weight: bold;
        }
        &:hover {
          background: #ffffff;
        }
        &:active {
          background: #f1f2f4;
        }
      }
    }
  }

  .dialog-right {
    width: calc(100% - 220px);
  }
}
</style>
