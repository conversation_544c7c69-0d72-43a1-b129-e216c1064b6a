<template>
  <div class="user-info" :class="{ 'is-separate': props.isSeparate }">
    <div class="server">
      <img src="@/assets/img/ai/server-icon.svg" @click="handleContactUs" />
    </div>

    <el-popover
      :visible="moreVisible"
      placement="bottom-end"
      :width="250"
      :show-arrow="false"
      popper-class="more-menu-popper"
      class="more-menu-popper"
    >
      <div v-clickOutside:more-menu="() => (moreVisible = false)" class="more-popper">
        <div class="menu-header">
          <div class="avatar-box">
            <img src="@/assets/img/ai/user-avatar.svg" class="avatar" />
            <img src="@/assets/img/ai/ai-pro.svg" class="pro" />
          </div>
          <div class="info">
            <div class="name-text">{{ userInfo?.name }}</div>
            <div class="email-text">{{ userInfo?.email }}</div>
          </div>
        </div>
        <div class="menu-divider"></div>
        <div class="more-menu">
          <div class="menu-item" @click="goToWebsite">
            <img src="@/assets/img/ai/user/website-icon.svg" />
            <span>Go to Website</span>
          </div>
          <div class="menu-item" @click="handleInfoDialog">
            <img src="@/assets/img/ai/user/user-icon.svg" />
            <span>User Information</span>
          </div>
          <div class="menu-item" @click="handlePricingDialog">
            <img src="@/assets/img/ai/user/king-icon.svg" />
            <span>Active plan</span>
          </div>
        </div>
        <div class="menu-divider"></div>
        <div class="more-menu">
          <div class="menu-item" @click="handleLogout">
            <img src="@/assets/img/ai/user/sign-out-icon.svg" />
            <span>Sign Out</span>
          </div>
        </div>
      </div>
      <template #reference>
        <div v-if="isLogin" class="user-avatar" @click.stop="moreVisible = true">
          <img src="@/assets/img/ai/user-avatar.svg" class="avatar" />
          <img src="@/assets/img/ai/ai-pro.svg" class="pro" />
        </div>
        <div v-else class="login">
          <div class="login-btn" @click="handleLogin">Sign in / Sign up</div>
        </div>
      </template>
    </el-popover>
  </div>

  <aiContactUsDialog v-model:visible="contactUsDialogVisible" />
  <aiSettingsDialog v-model:visible="userInfoDialogVisible" />
  <aiPricingDialog v-model:visible="pricingDialogVisible" @enterprise="handleEnterpriseDialog" />
  <aiEnterpriseDialog v-model:visible="enterpriseDialogVisible" />
</template>

<script setup lang="ts">
import aiContactUsDialog from '@/components/ai-contact-us-dialog.vue'
import aiEnterpriseDialog from '@/components/ai-enterprise-dialog.vue'
import aiPricingDialog from '@/components/ai-pricing-dialog.vue'
import aiSettingsDialog from '@/components/user/ai-settings-dialog.vue'
import { useUserStore } from '@/store/user'

defineComponent({
  name: 'UserStatus',
})

const props = defineProps({
  isSeparate: {
    type: Boolean,
    default: true,
  },
})

const { $SKToast } = useNuxtApp()
const userStore = useUserStore()
const userInfo = computed(() => userStore.info)
const isLogin = computed(() => userStore.isLogin)

// 在隐藏弹出层前释放当前焦点，避免 aria-hidden 与保留焦点冲突
const releaseFocusBeforeHide = () => {
  if (process.client) {
    const active = document.activeElement as HTMLElement | null
    if (active && typeof active.blur === 'function') {
      active.blur()
    }
  }
}

const handleLogin = () => {
  // 由主应用管理登录状态
  if (window.$wujie) {
    window.$wujie.bus.$emit('login')
  } else {
    // TODO: 未在子应用实现的功能
    $SKToast.error('主应用未启动')
  }

}

const contactUsDialogVisible = ref(false)
const handleContactUs = () => {
  contactUsDialogVisible.value = true
}

const userInfoDialogVisible = ref(false)
const handleInfoDialog = () => {
  releaseFocusBeforeHide()
  moreVisible.value = false
  userInfoDialogVisible.value = true
}

const pricingDialogVisible = ref(false)
const handlePricingDialog = () => {
  releaseFocusBeforeHide()
  moreVisible.value = false
  pricingDialogVisible.value = true
}

const enterpriseDialogVisible = ref(false)
const handleEnterpriseDialog = () => {
  releaseFocusBeforeHide()
  moreVisible.value = false
  enterpriseDialogVisible.value = true
}

const goToWebsite = () => {
  releaseFocusBeforeHide()
  moreVisible.value = false
  if (window.$wujie) {
    window.$wujie.bus.$emit('goToWebsite')
  } else {
    // TODO: 未在子应用实现的功能
    $SKToast.error('主应用未启动')
  }
}
const handleLogout = () => {
  releaseFocusBeforeHide()
  moreVisible.value = false
  if (window.$wujie) {
    window.$wujie.bus.$emit('logout')
  } else {
    // TODO: 未在子应用实现的功能
    $SKToast.error('主应用未启动')
  }
}

const moreVisible = ref(false)
</script>

<style lang="scss" scoped>
.user-info {
  width: fit-content;
  height: 36px;
  margin-right: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  gap: 20px;

  .server {
    margin-top: 1px;

    img {
      width: 32px;
      height: 32px;
      user-select: none;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #e2e2e2;
        border-radius: 6px;
      }
    }
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    position: relative;
    user-select: none;
    cursor: pointer;

    img {
      width: 32px;
      height: 32px;
    }

    .pro {
      width: 43px;
      height: 43px;
      position: absolute;
      top: -5px;
      right: -6px;
    }
  }

  .login-btn {
    width: fit-content;
    height: 32px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 8px;
    background: #ff7700;

    font-size: 12px;
    font-weight: bold;
    color: #ffffff;
    user-select: none;
    cursor: pointer;

    transition: background 0.2s;

    &:hover {
      background: lighten($color: #ff7700, $amount: 10%);
    }

    &:active {
      background: darken($color: #ff7700, $amount: 10%);
    }
  }
}

.is-separate {
  gap: 34px;

  .server {
    position: relative;

    &::before {
      content: '';
      width: 2px;
      height: 18px;
      right: -16px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 3;
      background-color: #d8d8d8;
      position: absolute;
      display: block;
      pointer-events: none;
    }
  }
}
</style>

<style lang="scss">
.el-popover.el-popper {
  padding: 20px 20px 8px 20px;
  border-radius: 12px;
  box-shadow: 0px 4px 16px 0px rgba(173, 190, 215, 0.25);
}

.more-menu-popper {
  .more-popper {
    .menu-header {
      display: flex;
      align-items: center;
      gap: 8px;
      padding-bottom: 10px;

      .avatar-box {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        transform: scale(1.3);

        .avatar {
          width: 30px;
          height: 30px;
        }

        .pro {
          width: 37px;
          height: 37px;
          position: absolute;
          top: 2px;
          right: 1px;
        }
      }

      .info {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .name-text {
          font-size: 16px;
          font-weight: 500;
          color: #171a23;
        }

        .email-text {
          font-size: 12px;
          font-weight: 350;
          color: #9295ab;
        }

        .name-text,
        .email-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .more-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .menu-item {
        display: flex;
        align-items: center;
        padding: 6px;
        border-radius: 6px;
        gap: 12px;
        font-size: 16px;
        color: #171a23;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s ease;

        img {
          width: 16px;
          height: 16px;
        }

        &:hover {
          background: #f5f5f5;
        }

        &:active {
          background: darken($color: #f5f5f5, $amount: 10%);
        }
      }
    }

    .menu-divider {
      width: 100%;
      height: 1px;
      margin: 10px 0;
      background: #e5e5e5;
    }
  }
}
</style>
