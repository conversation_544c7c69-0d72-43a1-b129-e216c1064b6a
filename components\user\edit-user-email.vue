<template>
  <div class="dialog-panel">
    <div class="panel-title">
      {{ isVerified ? 'Identity Verification' : 'Verify New Email' }}
    </div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      class="panel-content"
      @submit.prevent="handleConfirm"
    >
      <el-form-item prop="email">
        <el-input
          v-model="form.email"
          :disabled="!isVerified"
          placeholder="Please enter email"
        />
      </el-form-item>
      <el-form-item prop="verifyCode">
        <div class="verify-code">
          <el-input
            v-model="form.verifyCode"
            placeholder="Enter Verification Code"
          />
          <div class="verify-code-btn" @click="sendVerifyCode">
            {{ btnText }}
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div class="panel-footer">
      <div class="cancel" @click="closeDialog">Cancel</div>
      <div class="confirm" @click="handleConfirm">
        {{ isVerified ? 'Confirm' : 'Next' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
const { $SKToast } = useNuxtApp()

const props = defineProps<{
  email: string;
}>()

const emit = defineEmits<{
  (e: 'close'): void;
}>()

const form = ref({
  email: '',
  verifyCode: '',
})
onMounted(() => {
  form.value.email = props.email
})

const rules = ref({
  email: [
    { required: true, message: 'Please enter email', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (!isEmail(value)) {
          callback(new Error('Please enter valid email address'))
          return
        }
        callback()
      },
    },
  ],
  verifyCode: [
    {
      required: true,
      message: 'Please enter verification code',
      trigger: 'blur',
    },
  ],
})

const formRef = ref<FormInstance>()
const closeDialog = () => {
  form.value.email = ''
  form.value.verifyCode = ''
  isVerified.value = false
  isSending.value = false
  countdown.value = 0
  formRef.value?.resetFields()
  emit('close')
}
const handleConfirm = () => {
  formRef.value?.validate((valid: boolean) => {
    if (!isSending.value) {
      $SKToast.warning('Please send verification code first')
      return
    }
    if (!valid) {
      return
    }
    if (!isVerified.value) {
      verifyOldEmail()
    } else {
      verifyNewEmail()
    }
  })
}

const isVerified = ref(false)
const isSending = ref(false)
const countdown = ref(0)
const btnText = computed(() => {
  return countdown.value > 0
    ? `Resend (${countdown.value}s)`
    : 'Send verification code'
})

const sendVerifyCode = () => {
  if (countdown.value > 0) return

  isSending.value = true
  countdown.value = 60

  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      isSending.value = false
    }
  }, 1000)
}

const verifyOldEmail = () => {
  form.value.email = ''
  form.value.verifyCode = ''
  formRef.value?.resetFields()

  isSending.value = false
  countdown.value = 0

  isVerified.value = true
}
const verifyNewEmail = () => {
  closeDialog()
  $SKToast.warning(`successfully!`)
}
</script>

<style scoped lang="scss">
.dialog-panel {
  width: 400px;
  border-radius: 8px;
  box-sizing: border-box;
  background: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .panel-title {
    width: 100%;
    padding: 17px 16px;
    font-size: 16px;
    color: #171a23;
    border-bottom: 1px solid #eeeeee;
  }
  .panel-content {
    width: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    box-sizing: border-box;

    .el-form-item {
      width: 100%;
    }

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: none;
      box-shadow: none;
      background: #f7f8fc;
    }
    .el-input {
      width: 100%;
      border-radius: 8px;
      border: 1px solid transparent;
      transition: all 0.3s ease;
      &:hover,
      &:focus,
      &:focus-within {
        border: 1px solid #d9d9d9;
      }
    }
    :deep(.el-input__inner) {
      &::placeholder {
        color: #9295ab;
      }
    }
    :deep(.el-input.is-disabled .el-input__inner) {
      -webkit-text-fill-color: #333333;
    }

    :deep(.el-form-item__error) {
      padding-top: 0;
      padding-left: 5px;
    }

    .verify-code {
      display: flex;
      gap: 16px;
      .verify-code-btn {
        width: 160px;
        height: 40px;
        border-radius: 6px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ffffff;
        box-sizing: border-box;
        border: 1px solid #eeeeee;
        font-size: 14px;
        font-weight: 350;
        color: #171a23;
        user-select: none;
        cursor: pointer;
        transition: all 0.3s ease;
        &.disabled {
          color: #9295ab;
          cursor: not-allowed;
          border-color: #eeeeee;
          &:hover,
          &:active {
            color: #9295ab;
            border-color: #eeeeee;
          }
        }
        &:not(.disabled) {
          &:hover {
            color: darken(#171a23, 10%);
            border-color: darken(#eeeeee, 20%);
          }
          &:active {
            color: darken(#171a23, 20%);
            border-color: darken(#eeeeee, 30%);
          }
        }
      }
    }
  }
  .panel-footer {
    display: flex;
    justify-content: flex-end;
    padding: 8px 16px 16px 16px;
    gap: 16px;
    .cancel,
    .confirm {
      width: 85px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;

      font-size: 14px;
      font-weight: bold;
      color: #6c6f76;
      user-select: none;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .cancel {
      border: 1px solid #d9d9d9;
      color: #6c6f76;
      &:hover {
        background: lighten($color: #d9d9d9, $amount: 10);
      }
      &:active {
        background: darken($color: #d9d9d9, $amount: 10);
      }
    }
    .confirm {
      color: #ffffff;
      background: #ff6600;
      &:hover {
        background: lighten($color: #ff6600, $amount: 10);
      }
      &:active {
        background: darken($color: #ff6600, $amount: 10);
      }
    }
  }
}
</style>
