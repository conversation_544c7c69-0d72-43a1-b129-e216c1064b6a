<template>
  <div class="dialog-panel">
    <div class="panel-title">Change Occupation</div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      class="panel-content"
      @submit.prevent="handleConfirm"
    >
      <el-form-item prop="occupation">
        <el-input
          v-model="form.occupation"
          placeholder="Please enter occupation"
        />
      </el-form-item>
    </el-form>
    <div class="panel-footer">
      <div class="cancel" @click="closeDialog">Cancel</div>
      <div class="confirm" @click="handleConfirm">Confirm</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
const { $SKToast } = useNuxtApp()

const emit = defineEmits<{
  (e: 'close'): void;
}>()

const form = ref({
  occupation: '',
})
const rules = ref({
  occupation: [
    { required: true, message: 'Please enter occupation', trigger: 'blur' },
  ],
})
const formRef = ref<FormInstance>()
const closeDialog = () => {
  form.value.occupation = ''
  formRef.value?.resetFields()
  emit('close')
}
const handleConfirm = () => {
  formRef.value?.validate((valid: boolean) => {
    if (!valid) return
    closeDialog()
    $SKToast.warning(`successfully!`)
  })
}
</script>

<style scoped lang="scss">
.dialog-panel {
  width: 400px;
  border-radius: 8px;
  box-sizing: border-box;
  background: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .panel-title {
    width: 100%;
    padding: 17px 16px;
    font-size: 16px;
    color: #171a23;
    border-bottom: 1px solid #eeeeee;
  }
  .panel-content {
    width: 100%;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;

    .el-form-item {
      width: 100%;
    }

    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: none;
      box-shadow: none;
      background: #f7f8fc;
    }
    .el-input {
      width: 100%;
      border-radius: 8px;
      border: 1px solid transparent;
      transition: all 0.3s ease;
      &:hover,
      &:focus,
      &:focus-within {
        border: 1px solid #d9d9d9;
      }
    }
    :deep(.el-input__inner) {
      &::placeholder {
        color: #9295ab;
      }
    }
    :deep(.el-form-item__error) {
      padding-top: 0;
      padding-left: 5px;
    }
  }
  .panel-footer {
    display: flex;
    justify-content: flex-end;
    padding: 8px 16px 16px 16px;
    gap: 16px;
    .cancel,
    .confirm {
      width: 85px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;

      font-size: 14px;
      font-weight: bold;
      color: #6c6f76;
      user-select: none;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .cancel {
      border: 1px solid #d9d9d9;
      color: #6c6f76;
      &:hover {
        background: lighten($color: #d9d9d9, $amount: 10);
      }
      &:active {
        background: darken($color: #d9d9d9, $amount: 10);
      }
    }
    .confirm {
      color: #ffffff;
      background: #ff6600;
      &:hover {
        background: lighten($color: #ff6600, $amount: 10);
      }
      &:active {
        background: darken($color: #ff6600, $amount: 10);
      }
    }
  }
}
</style>
