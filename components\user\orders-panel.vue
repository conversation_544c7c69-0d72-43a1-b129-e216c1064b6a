<template>
  <div class="panel">
    <div class="panel-title">Order History</div>
    <div class="panel-content">
      <el-table :data="renderDataList" header-cell-class-name="table-header">
        <el-table-column
          v-for="(item, index) in colEntity"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ?? 'auto'"
          :align="item.center ?? 'left'"
          class-name="table-row"
          header-align="left"
        >
          <template #default="{ row }">
            <div
              v-if="item.prop === 'order_number'"
              :content="row.title"
              class="order-number"
            >
              {{ row.order_number }}
            </div>
            <div v-else class="default-cell" :class="{ none: !row[item.prop] }">
              {{ row[item.prop] || '——' }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
const colEntity = ref([
  {
    prop: 'order_number',
    label: 'Order Number',
    width: '150px',
    center: 'left',
  },
  {
    prop: 'order_details',
    label: 'Order Details',
    width: '100px',
    center: 'left',
  },
  {
    prop: 'order_amount',
    label: 'Amount',
    width: '64px',
    center: 'left',
  },

  {
    prop: 'payment_time',
    label: 'Payment Time',
    width: '110px',
    center: 'left',
  },
  {
    prop: 'payment_method',
    label: 'Payment Method',
    width: 'auto',
    center: 'left',
  },
  {
    prop: 'order_status',
    label: 'Order Status',
    width: '86px',
    center: 'left',
  },
])
const renderDataList = ref([
  {
    order_number: '1234567890',
    order_details: 'Order Details',
    order_amount: '100',
    payment_time: '2021-01-01',
    payment_method: 'Payment Method',
  },
])
</script>

<style scoped lang="scss">
.panel {
  width: 100%;
  padding: 25px 16px;
  .panel-title {
    margin-bottom: 8px;
    padding-bottom: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #171a23;
    border-bottom: 1px solid #f1f2f4;
  }
  .panel-content {
    margin-top: 20px;
    :deep(.table-header) {
      height: 44px;
      padding: 0;
      font-size: 12px;
      font-weight: 500;
      color: #6c6f76;
      line-height: 44px;
      background: #f9fafb;
      font-weight: 600 !important;
    }

    :deep(.el-table__header) {
      width: 100% !important;
      letter-spacing: -0.5px;
    }

    :deep(.el-table__header-wrapper) {
      z-index: 100;
    }

    :deep(.table-row) {
      font-weight: 400;
      height: 44px;
      padding: 0 10px;

      .cell {
        padding: 0 6px;
        white-space: nowrap;
      }
      .order-number {
        font-size: 12px;
        font-weight: 500;
        line-height: normal;
        color: #111827;
      }
      .default-cell {
        @include text-multi-line(2);
        font-size: 14px;
        color: #333333;
      }
      .none {
        color: #999999;
      }
    }
    :deep(.el-table__cell) {
      padding: 0;
    }
  }
}
</style>
