<template>
  <div class="panel">
    <div class="panel-title">Base Info</div>
    <div class="content-part">
      <div class="option-item base">
        <div class="avatar-box">
          <img :src="userInfo.avatar" class="avatar" />
          <div class="info">
            <div class="name">{{ userInfo.nickname }}</div>
            <el-upload
              class="avatar-upload"
              :limit="1"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleAvatarChange"
              accept="image/jpeg,image/png,image/jpg"
            >
              <template #trigger>
                <div class="upload-btn">Upload Avatar</div>
              </template>
            </el-upload>
          </div>
        </div>
        <div class="change-btn" @click="handleChangeNickname()">
          Change Nickname
        </div>
      </div>
      <div v-for="item in infoList" :key="item.key" class="option-item">
        <div class="value-box">
          <div class="label">{{ item.title }}</div>
          <div class="value">{{ userInfo[item.key] }}</div>
        </div>
        <div class="change-btn" @click="item.fun">{{ item.changeText }}</div>
      </div>
    </div>
    <div class="panel-title">Account security</div>
    <div class="content-part">
      <div v-for="item in securityList" :key="item.key" class="option-item">
        <div class="value-box">
          <div class="label">{{ item.title }}</div>
          <div class="value">{{ userInfo[item.key] }}</div>
        </div>
        <div class="change-btn" @click="item.fun">{{ item.changeText }}</div>
      </div>
    </div>
  </div>

  <!-- 动态加载的对话框组件 -->
  <el-dialog
    v-model="dialogVisible"
    width="400px"
    :show-close="true"
    destroy-on-close
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <component
      :is="dialogComponent"
      v-bind="dialogProps"
      @close="dialogVisible = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
import type { UploadFile, UploadUserFile } from 'element-plus'
import baseAvatar from '@/assets/img/ai/user-avatar.svg'

const EditUserNameComponent = defineAsyncComponent({
  loader: () => import('@//components/user/edit-user-name.vue'),
  delay: 300,
})
const EditUserNicknameComponent = defineAsyncComponent({
  loader: () => import('@//components/user/edit-user-nickname.vue'),
  delay: 300,
})
const EditUserOccupationComponent = defineAsyncComponent({
  loader: () => import('@//components/user/edit-user-occupation.vue'),
  delay: 300,
})
const EditUserCompanyComponent = defineAsyncComponent({
  loader: () => import('@//components/user/edit-user-company.vue'),
  delay: 300,
})
const EditUserEmailComponent = defineAsyncComponent({
  loader: () => import('@//components/user/edit-user-email.vue'),
  delay: 300,
})
const EditUserPasswordComponent = defineAsyncComponent({
  loader: () => import('@//components/user/edit-user-password.vue'),
  delay: 300,
})

const { $SKToast } = useNuxtApp()

const baseInfo = {
  avatar: '',
  name: 'John Doe',
  nickname:
    'John DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn DoeJohn Doe',
  email:
    '<EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> <EMAIL> ',
  phone: '1234567890',
  company: 'John Doe',
  position: 'John Doe',
  address: 'John Doe',
  city: 'John Doe',
}

const dialogVisible = ref(false)
const dialogComponent = shallowRef(null)
const dialogProps = ref({})

const infoList = [
  {
    title: 'Name',
    key: 'name',
    changeText: 'Change Name',
    fun: () => {
      dialogComponent.value = EditUserNameComponent
      dialogProps.value = {}
      dialogVisible.value = true
    },
  },
  {
    title: 'Company/Type',
    key: 'company',
    changeText: 'Change Company',
    fun: () => {
      dialogComponent.value = EditUserCompanyComponent
      dialogProps.value = {}
      dialogVisible.value = true
    },
  },
  {
    title: 'Occupation',
    key: 'position',
    changeText: 'Change Occupation',
    fun: () => {
      dialogComponent.value = EditUserOccupationComponent
      dialogProps.value = {}
      dialogVisible.value = true
    },
  },
]

const securityList = [
  {
    title: 'Email',
    key: 'email',
    changeText: 'Change Email',
    fun: () => {
      dialogComponent.value = EditUserEmailComponent
      dialogProps.value = {
        email: '<EMAIL>',
      }
      dialogVisible.value = true
    },
  },
  {
    title: 'Password',
    key: 'password',
    changeText: 'Add Password',
    fun: () => {
      dialogComponent.value = EditUserPasswordComponent
      dialogProps.value = {
        email: '<EMAIL>',
      }
      dialogVisible.value = true
    },
  },
]

const avatarUrl = ref('')
const avatarFile = ref<UploadUserFile>()
const handleAvatarChange = (uploadFile: UploadFile) => {
  const file = uploadFile.raw
  if (!file) return

  const isLessThan2M = file.size / 1024 / 1024 <= 2
  if (!isLessThan2M) {
    $SKToast.error('Cover image size must be less than 2MB!')
    return
  }

  avatarFile.value = uploadFile
  avatarUrl.value = URL.createObjectURL(file)
}

const userInfo = computed(() => ({
  ...baseInfo,
  avatar: baseInfo.avatar || avatarUrl.value || baseAvatar,
  password: 'Set Login Password',
}))

const handleChangeNickname = () => {
  dialogComponent.value = EditUserNicknameComponent
  dialogProps.value = {}
  dialogVisible.value = true
}
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  padding: 0;
  background: transparent;
  .el-dialog__header {
    display: none;
  }
}
.panel {
  width: 100%;
  height: 100%;
  padding: 25px 16px;
  box-sizing: border-box;
  .panel-title {
    margin-bottom: 16px;
    padding-bottom: 8px;
    font-size: 18px;
    font-weight: bold;
    color: #171a23;
    border-bottom: 1px solid #f1f2f4;
  }
  .content-part {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 25px;

    .option-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 25px;

      .avatar-box {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
        }
        .info {
          display: flex;
          flex-direction: column;
          gap: 4px;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .name {
            font-size: 16px;
            color: #171a23;

            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .upload-btn {
            font-size: 12px;
            color: #2259b9;
            user-select: none;
            cursor: pointer;
            &:hover {
              color: darken(#2259b9, 10%);
            }
            &:active {
              color: darken(#2259b9, 20%);
            }
          }
        }
      }
      .value-box {
        display: flex;
        flex-direction: column;
        gap: 4px;

        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .label {
          font-size: 14px;
          font-weight: 350;
          color: #171a23;
        }
        .value {
          font-size: 12px;
          font-weight: 350;
          color: #9295ab;

          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .change-btn {
        width: 120px;
        height: 32px;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;

        font-size: 12px;
        color: #171a23;
        background: #f7f8fa;
        box-sizing: border-box;
        border: 1px solid #f1f3f6;
        user-select: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.075, 0.82, 0.165, 1);
        &:hover {
          border-color: darken(#f7f8fa, 5%);
          background-color: darken(#f7f8fa, 5%);
        }
        &:active {
          border-color: darken(#f7f8fa, 10%);
          background-color: darken(#f7f8fa, 10%);
        }
      }
    }
  }
}
</style>
