import UnknownIcon from '@/assets/img/ai/file-icon/unknown.svg'
import type { FileType } from '@/constants/ai'
import { FileTypeIcon } from '@/constants/ai'

export const useFileTypeIcon = () => {
  const judgeFileType = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toUpperCase() || ''
    if (Object.keys(FileTypeIcon).includes(extension)) {
      return {
        type: extension as FileType,
        icon: FileTypeIcon[extension as keyof typeof FileTypeIcon],
      }
    }
    // 默认返回文本文件图标
    return {
      type: 'UNKNOWN' as FileType,
      icon: UnknownIcon,
    }
  }

  return {
    judgeFileType,
  }
}

export default useFileTypeIcon
