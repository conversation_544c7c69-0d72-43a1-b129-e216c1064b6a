import type { FetchResponse } from 'ofetch'
import { $fetch } from 'ofetch'
import requestCode from '@/constants/request-code'
import devEnv from '@/env/env.dev'
import proEnv from '@/env/env.pro'
import { useUserStore } from '@/store/user'

import type { UseFetchOptions } from '#app'
import { useCookie, useFetch, useNuxtApp, useRequestHeaders } from '#app'

// useHttp传递给useFetch接受的配置参数
interface ConfigOptions extends UseFetchOptions<any> {
  headers?: Record<string, string>;
  method?: string;
  params?: Record<string, any>;
  body?: Record<string, any>;
}

// 自定义配置，例如是否需要返回原始 useFetch 结果
interface CustomConfig {
  originalResult?: boolean; // 返回原始 useFetch 数据结构
}

// 服务端返回的基础结构类型 (请根据你的实际API返回结构调整)
interface ApiResponse<T = any> {
  data: T;
  message: string;
  code: number;
}

// 标准化返回结构
class StandardResponse<T = any> {
  success: boolean
  data: T
  message: string
  code: number
  constructor(initParam: {
    success: boolean;
    data: T;
    message: string;
    code: number;
  }) {
    const { success, data, message, code } = initParam
    this.success = success
    // 确保 data 有一个默认值，防止解构时出错
    this.data = data || ({} as T)
    this.message = message || ''
    this.code = code || 0
  }
}

const env = process.env.NODE_ENV === 'development' ? devEnv : proEnv

/**
 * 核心 HTTP 请求函数
 * @param url 请求的相对路径
 * @param options useFetch 的配置选项
 */
const useHttp = <T = any>(url: string, options?: ConfigOptions) => {
  const clientHeaders = process.server ? useRequestHeaders(['cookie']) : {}
  const defaultHeaders: Record<string, string> = {
    'Sk-Lang': 'en',
    'Sk-Platform': 'web_pc',
    'Sk-Version': 'v0',
  }
  const nuxtApp = useNuxtApp()
  // 获取 Pinia 实例
  const pinia = nuxtApp.$pinia
  let userStore: any = null
  if (pinia) {
    try {
      userStore = useUserStore(pinia)
    } catch (e) {
      console.error('Failed to get user store:', e)
      // 在这里可以添加错误处理逻辑，比如重定向到错误页或显示提示
    }
  }

  const token = useCookie<string | null>('token') // 获取 token cookie

  // 如果 token 存在，则添加到默认请求头中
  if (token.value) {
    defaultHeaders.Authorization = `Bearer ${token.value}`
  }

  // 合并传入的 headers 和默认 headers
  const headers = {
    ...clientHeaders,
    ...defaultHeaders,
    ...(options?.headers || {}),
  }

  // 准备 useFetch 的配置
  const fetchOptions: UseFetchOptions<ApiResponse<T>> = {
    baseURL: `${env.base}/sokoo`, // 基础 URL + API 前缀
    headers,
    immediate: true, // 确保请求立即执行
    ...options,
    // 响应拦截器
    onResponse({ response }: { response: FetchResponse<ApiResponse<T>> }) {
      const data = response._data

      // 检查是否需要登录且 token 存在
      if (data?.code === requestCode.NEED_LOGIN && token.value && userStore) {
        // 使用 runWithContext 确保在 Nuxt 上下文中执行 logout
        nuxtApp
          .runWithContext(() => userStore!.logout())
          .catch((err: any) => console.error('Logout failed:', err))
      }
      // 可添加更多的响应拦截逻辑
    },
    // 错误处理拦截器
    onResponseError({ response }: { response: FetchResponse<ApiResponse<T>> }) {
      // 全局错误处理
      console.error(
        'HTTP Response Error:',
        response.status,
        response.statusText,
        response._data,
      )
    },
  }

  // 发起请求并确保有结果
  return useFetch<ApiResponse<T>>(url, fetchOptions)
}

/**
 * 生成通用的请求模型函数
 * @param url 请求的相对路径
 * @param method HTTP 请求方法
 * @param customConfig 自定义配置
 */
const getCommonModel = <Req = any, Res = any>(
  url: string,
  method: ConfigOptions['method'],
  customConfig: CustomConfig = {},
) => {
  const { originalResult } = customConfig

  /**
   * 返回的异步请求函数
   * @param params 请求参数 (GET 时为 query, POST/PUT 时为 body)
   */
  return async (
    params?: Req,
  ): Promise<StandardResponse<Res | ApiResponse<Res>>> => {
    try {
      // 配置请求选项
      const options: ConfigOptions = { method }
      if (
        method?.toUpperCase() === 'GET' ||
        method?.toUpperCase() === 'DELETE'
      ) {
        options.params = params as Record<string, any>
      } else {
        options.body = params as Record<string, any>
      }

      // 执行请求
      const {
        data: result,
        error,
        status,
        execute,
      } = await useHttp<Res>(url, options)

      // 处理idle状态 - 尝试手动执行请求
      if (status.value === 'idle') {
        try {
          await execute()
        } catch (execError) {
          console.error('Request execution error:', execError)
        }
      }

      // 如果重试后仍为idle状态或result为null，尝试使用$fetch
      if (status.value === 'idle' || result.value === null) {
        try {
          const fullUrl = `${env.base}/sokoo${url}`
          const fetchOptions: any = {
            method,
            headers: options.headers,
          }

          if (
            method?.toUpperCase() === 'GET' ||
            method?.toUpperCase() === 'DELETE'
          ) {
            // GET/DELETE请求，参数放在URL中
            const queryParams = new URLSearchParams(
              params as Record<string, string>,
            ).toString()
            const fetchUrl = queryParams ? `${fullUrl}?${queryParams}` : fullUrl

            const fetchResult = await $fetch<ApiResponse<Res>>(
              fetchUrl,
              fetchOptions,
            )

            if (fetchResult) {
              return new StandardResponse<Res>({
                success: fetchResult.code === 0 || fetchResult.code === 200,
                message: fetchResult.message || '',
                data: (fetchResult.data || {}) as Res,
                code: fetchResult.code,
              })
            }
          } else {
            // POST/PUT等请求，参数放在body中
            fetchOptions.body = params

            const fetchResult = await $fetch<ApiResponse<Res>>(
              fullUrl,
              fetchOptions,
            )

            if (fetchResult) {
              return new StandardResponse<Res>({
                success: fetchResult.code === 0 || fetchResult.code === 200,
                message: fetchResult.message || '',
                data: (fetchResult.data || {}) as Res,
                code: fetchResult.code,
              })
            }
          }
        } catch (fetchError: any) {
          console.error('$fetch备用方案失败:', fetchError)
        }

        // 如果$fetch也失败，返回默认错误响应
        return new StandardResponse<Res>({
          success: false,
          message:
            status.value === 'idle' ? '请求未能开始执行' : '请求未返回数据',
          data: {} as Res,
          code: -1,
        })
      }

      // 处理 useFetch 错误
      if (status.value === 'error' || error.value) {
        const statusCode = error.value?.statusCode || 500
        const statusMessage = error.value?.statusMessage || '请求失败'
        // 尝试从 error.value.data 获取更详细的后端错误信息
        const backendMessage =
          (error.value?.data as ApiResponse)?.message || statusMessage
        const backendCode =
          (error.value?.data as ApiResponse)?.code || statusCode

        return new StandardResponse<Res>({
          success: false,
          message: `${statusCode} ${backendMessage}`,
          data: {} as Res,
          code: backendCode,
        })
      }

      // 使用默认响应数据处理
      const responseData = result.value || {
        code: -1,
        message: '数据为空',
        data: {} as Res,
      }

      // 返回原始结果格式
      if (originalResult) {
        return new StandardResponse<ApiResponse<Res>>({
          success: true,
          message: '',
          data: responseData,
          code: 0,
        })
      }

      // 解构后端返回的数据
      const { code, message, data } = responseData

      // 处理业务逻辑错误
      if (code !== 0 && code !== 200) {
        return new StandardResponse<Res>({
          success: false,
          message: message || '请求处理失败',
          data: (data || {}) as Res,
          code: code,
        })
      }

      // 请求成功
      return new StandardResponse<Res>({
        success: true,
        message: message || '',
        data: (data || {}) as Res,
        code: code,
      })
    } catch (e: any) {
      return new StandardResponse<Res>({
        success: false,
        message: e.message || '未知错误',
        data: {} as Res,
        code: e.cause || -1,
      })
    }
  }
}

/**
 * 生成 GET 请求的模型函数
 * @param url 请求的相对路径
 * @param customConfig 自定义配置
 * @returns 返回一个接受查询参数并返回 Promise<StandardResponse> 的函数
 */
const get = <Req = any, Res = any>(url: string, customConfig?: CustomConfig) =>
  getCommonModel<Req, Res>(url, 'GET', customConfig)

/**
 * 生成 POST 请求的模型函数
 * @param url 请求的相对路径
 * @param customConfig 自定义配置
 * @returns 返回一个接受请求体参数并返回 Promise<StandardResponse> 的函数
 */
const post = <Req = any, Res = any>(url: string, customConfig?: CustomConfig) =>
  getCommonModel<Req, Res>(url, 'POST', customConfig)

export { get, post }
