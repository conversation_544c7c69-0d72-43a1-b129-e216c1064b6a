import requestCode from '@/constants/request-code'
import devEnv from '@/env/env.dev'
import proEnv from '@/env/env.pro'
import { useUserStore } from '@/store/user'

// useHttp传递给useFetch接受的配置参数
interface ConfigOptions {
  method?:
    | 'GET'
    | 'HEAD'
    | 'PATCH'
    | 'POST'
    | 'PUT'
    | 'DELETE'
    | 'CONNECT'
    | 'OPTIONS'
    | 'TRACE'
    | 'get'
    | 'head'
    | 'patch'
    | 'post'
    | 'put'
    | 'delete'
    | 'connect'
    | 'options'
    | 'trace';
  query?: any;
  params?: any;
  body?: any;
  headers?: any;
  baseUrl?: string;
}
interface CustomConfig {
  originalResult?: boolean; // 返回原始数据
}

// modal层返回的标准结构
class StandardResponse {
  success: boolean
  data: any
  message: string
  code: number
  constructor(initParam: {
    success: boolean;
    data: any;
    message: string;
    code: number;
  }) {
    const { success, data, message, code } = initParam
    this.success = success
    this.data = data || {}
    this.message = message || ''
    this.code = code || 0
  }
}

const env = process.env.NODE_ENV === 'development' ? devEnv : proEnv

export const useHttp = (url: string, options?: ConfigOptions) => {
  const clientHeaders = process.server ? useRequestHeaders(['cookie']) : {}
  const defaultHeaders: any = {
    'Sk-Lang': 'en',
    'Sk-Platform': 'web_pc',
    'Sk-Version': 'v0',
  }
  const nuxtApp = useNuxtApp()
  const userStore = useUserStore(nuxtApp.$pinia)
  const token = useCookie('token')
  if (token.value) {
    defaultHeaders.Authorization = `Bearer ${token.value}`
  }
  const headers =
    typeof options?.headers === 'object'
      ? {
          ...clientHeaders,
          ...defaultHeaders,
          ...options.headers,
        }
      : {
          ...clientHeaders,
          ...defaultHeaders,
        }
  return useFetch(`${env.base}/sokoo${url}`, {
    headers,
    ...options,
    onResponse: function ({ response }) {
      const data = response._data
      if (data?.code === requestCode.NEED_LOGIN && token.value) {
        // 这里的runWithContext是因为logout方法中用到了useCookie,所以logout方法需要运行在nuxtContext中,特殊处理
        // 此处没有nuxtContext的原因参考https://nuxt.com/docs/api/composables/use-nuxt-app#runwithcontext,可能是因为异步请求
        // 正常情况在composable中调用是会有nuxtContext的
        nuxtApp.runWithContext(userStore.logout)
      }
      return response
    },
  })
}

// 根据url生成相应的通用模板方法
export const getCommonModel = (
  url: string,
  method: ConfigOptions['method'],
  customConfig: CustomConfig = {},
) => {
  const { originalResult } = customConfig
  return async (params?: any) => {
    try {
      const {
        data: result,
        status,
        error,
      } = await useHttp(url, {
        method,
        params,
        body: params,
      })

      if (status.value !== 'success') {
        const { statusMessage, statusCode } = error.value as {
          statusMessage: string;
          statusCode: number;
        }
        return new StandardResponse({
          success: false,
          message: `${statusCode} ${statusMessage}`,
          data: {},
          code: statusCode,
        })
      }

      if (originalResult) {
        return {
          success: true,
          message: '',
          data: result.value,
        }
      }
      const { code, message, data } = result.value as {
        code: number;
        message: string;
        data: any;
      }
      const apiData = data || {}
      if (code) {
        throw new Error(message, { cause: code })
      }
      return new StandardResponse({
        success: true,
        message,
        data: apiData,
        code,
      })
    } catch (e: any) {
      console.error(e)
      return new StandardResponse({
        success: false,
        message: e.message,
        data: {},
        code: e.cause,
      })
    }
  }
}

export const getGetModel = (url: string, customConfig?: CustomConfig) =>
  getCommonModel(url, 'get', customConfig)

export const getPostModel = (url: string, customConfig?: CustomConfig) =>
  getCommonModel(url, 'post', customConfig)
