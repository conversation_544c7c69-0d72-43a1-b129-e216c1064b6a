# useLocalStorage

这是一个封装 localStorage 操作的 Vue 3 组合式 API，提供了类型安全、过期时间和响应式数据支持。所有存储的键名都会自动添加`plasdata_`前缀，以避免与其他应用的数据冲突。

## 基本用法

```typescript
import { useLocalStorage } from '@/composables/useLocalStorage'

// 初始化 (默认前缀为 'plasdata_')
const storage = useLocalStorage()

// 基本存储操作
storage.setItem('my-key', 'my-value') // 实际存储为 'plasdata_my-key'

// 获取数据
const value = storage.getItem('my-key') // 获取 'plasdata_my-key' 的值
const user = storage.getItem<{ name: string; age: number }>('user') // 类型安全的获取

// 检查是否存在
const exists = storage.hasItem('my-key') // 返回: true/false

// 删除数据
storage.removeItem('my-key')

// 清空所有Plasdata相关数据 (只清除带plasdata_前缀的数据)
storage.clear()

// 获取所有键 (不含前缀)
const allKeys = storage.keys() // 返回: ['my-key', 'user', ...]

// 获取已用空间大小（KB）(只计算带plasdata_前缀的数据)
const size = storage.size()
```

## 响应式数据

最强大的功能是 `useStorage` 方法，它提供了响应式的 localStorage 数据：

```typescript
// 在组件中使用
const username = storage.useStorage('username', '') // 第二个参数是默认值

// 直接在模板中使用
<input v-model="username" />

// 在JS/TS中使用和修改
console.log(username.value) // 获取值
username.value = '新用户名' // 修改值（会自动保存到localStorage）
```

## 自定义前缀 (通常不需要)

如果确实需要使用不同的前缀，可以在初始化时指定：

```typescript
// 使用自定义前缀
const customStorage = useLocalStorage({ prefix: 'custom_' })

// 使用自定义前缀存储
customStorage.setItem('key', 'value') // 实际存储为 'custom_key'
```

不过在大多数情况下，建议使用默认的 `plasdata_` 前缀，保持一致性。

## 在 SSR/SSG 环境中使用

该组件支持服务器端渲染(SSR)和静态站点生成(SSG)：

```typescript
// 在任何环境中安全使用
const username = storage.useStorage('username', '')

// 在模板中建议使用ClientOnly组件包裹
<ClientOnly>
  <input v-model="username" />
</ClientOnly>
```

所有方法都会检测当前环境，在服务器端会安全地跳过 localStorage 操作。

## 注意事项

1. 存储的数据会自动序列化为 JSON，所以可以直接存储对象
2. 带有过期时间的数据会在过期后自动清除
3. 读取不存在或已过期的数据会返回 null
4. 响应式数据在组件卸载后仍会保留在 localStorage 中
5. 修改复杂对象属性时，需要确保使用深度监听
6. 在 SSR/SSG 环境中，初始值总是默认值，直到客户端激活后才会读取 localStorage
7. 所有键名都会自动添加`plasdata_`前缀，但 API 调用时不需要包含前缀
