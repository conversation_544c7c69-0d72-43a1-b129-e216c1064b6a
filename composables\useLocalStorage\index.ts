import type { Ref } from 'vue'
import { onMounted, ref, watch } from 'vue'

// 检测是否为客户端环境
const isClient = typeof window !== 'undefined'

interface StorageData<T> {
  value: T;
  expire?: number; // 过期时间戳
}

interface LocalStorageOptions {
  prefix?: string; // 键名前缀
}

/**
 * localStorage 组合式API
 * 支持响应式数据、过期时间和JSON对象存储
 * @param options 配置选项，可设置前缀等
 */
export function useLocalStorage(options: LocalStorageOptions = {}) {
  const prefix = options.prefix || 'plasdata_' // 默认前缀为plasdata_

  /**
   * 获取带前缀的完整键名
   * @param key 原始键名
   * @returns 带前缀的完整键名
   */
  const getFullKey = (key: string): string => {
    return `${prefix}${key}`
  }

  /**
   * 存储数据到localStorage，支持过期时间
   * @param key 键名（将自动添加前缀）
   * @param value 值
   * @param expire 过期时间(单位:秒)，不传则永不过期
   */
  const setItem = <T>(key: string, value: T, expire?: number): void => {
    if (!isClient) return

    try {
      const data: StorageData<T> = {
        value,
      }

      // 如果设置了过期时间
      if (expire) {
        data.expire = new Date().getTime() + expire * 1000
      }

      localStorage.setItem(getFullKey(key), JSON.stringify(data))
    } catch (error) {
      console.error('LocalStorage存储失败:', error)
    }
  }

  /**
   * 从localStorage获取数据
   * @param key 键名（将自动添加前缀）
   * @returns 存储的值，如果已过期或不存在则返回null
   */
  const getItem = <T>(key: string): T | null => {
    if (!isClient) return null

    try {
      const dataStr = localStorage.getItem(getFullKey(key))
      if (!dataStr) return null

      const data: StorageData<T> = JSON.parse(dataStr)

      // 判断是否过期
      if (data.expire && data.expire < new Date().getTime()) {
        removeItem(key)
        return null
      }

      return data.value
    } catch (error) {
      console.error('LocalStorage读取失败:', error)
      return null
    }
  }

  /**
   * 创建响应式的localStorage数据
   * @param key 键名（将自动添加前缀）
   * @param defaultValue 默认值(当localStorage中不存在时使用)
   * @returns 响应式的数据ref对象
   */
  const useStorage = <T>(key: string, defaultValue: T) => {
    // 创建ref，初始值为默认值
    const value = ref<T>(defaultValue) as Ref<T>

    // 在客户端初始化时从localStorage读取值
    if (isClient) {
      try {
        // 立即尝试读取值
        const storedValue = getItem<T>(key)
        if (storedValue !== null) {
          value.value = storedValue
        }
      } catch (e) {
        console.error('读取localStorage失败:', e)
      }

      // 监听值变化并同步到localStorage
      watch(
        value,
        () => {
          try {
            setItem(key, value.value)
          } catch (e) {
            console.error('写入localStorage失败:', e)
          }
        },
        { deep: true },
      )
    }

    return value
  }

  /**
   * 删除localStorage中的某项数据
   * @param key 键名（将自动添加前缀）
   */
  const removeItem = (key: string): void => {
    if (!isClient) return
    localStorage.removeItem(getFullKey(key))
  }

  /**
   * 清空所有带前缀的localStorage数据
   * 注意：只会清除当前前缀下的数据
   */
  const clear = (): void => {
    if (!isClient) return

    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))
  }

  /**
   * 获取所有带前缀的key（返回不带前缀的原始键名）
   * @returns 所有当前前缀下的key数组
   */
  const keys = (): string[] => {
    if (!isClient) return []

    const result: string[] = []
    const prefixLength = prefix.length

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        result.push(key.substring(prefixLength))
      }
    }
    return result
  }

  /**
   * 判断localStorage中是否存在某个key，且未过期
   * @param key 键名（将自动添加前缀）
   * @returns 是否存在且未过期
   */
  const hasItem = (key: string): boolean => {
    if (!isClient) return false
    return getItem(key) !== null
  }

  /**
   * 获取当前前缀下已用空间大小(单位:KB)
   * @returns 已用空间大小
   */
  const size = (): number => {
    if (!isClient) return 0

    let totalSize = 0
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        totalSize += localStorage.getItem(key)?.length || 0
      }
    }
    return Math.round(totalSize / 1024)
  }

  return {
    prefix,
    setItem,
    getItem,
    useStorage,
    removeItem,
    clear,
    keys,
    hasItem,
    size,
  }
}
