import ExcelIcon from '@/assets/img/ai/file-icon/excel.svg'
import HtmlIcon from '@/assets/img/ai/file-icon/html.svg'
import MdIcon from '@/assets/img/ai/file-icon/md.svg'
import PdfIcon from '@/assets/img/ai/file-icon/pdf.svg'
import UnknownIcon from '@/assets/img/ai/file-icon/unknown.svg'
import WordIcon from '@/assets/img/ai/file-icon/word.svg'

export type FileStatus =
  | 'waiting'
  | 'indexing'
  | 'parsing'
  | 'splitting'
  | 'completed'
  | 'disabled'

export enum FileStatusLabel {
  waiting = 'Waiting',
  indexing = 'Indexing',
  parsing = 'Parsing',
  splitting = 'Splitting',
  completed = 'Available',
  disabled = 'Disabled',
}

export type FileType =
  | 'TXT'
  | 'MARKDOWN'
  | 'MDX'
  | 'PDF'
  | 'HTML'
  | 'XLSX'
  | 'XLS'
  | 'DOCX'
  | 'CSV'
  | 'MD'
  | 'HTM'
  | 'UNKNOWN'

export const FileTypeIcon = {
  TXT: WordIcon,
  MARKDOWN: MdIcon,
  MDX: MdIcon,
  PDF: PdfIcon,
  HTML: HtmlIcon,
  HTM: HtmlIcon,
  XLSX: ExcelIcon,
  XLS: ExcelIcon,
  DOCX: WordIcon,
  CSV: ExcelIcon,
  MD: MdIcon,
  UNKNOWN: UnknownIcon,
}
