export enum MessageType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
}

export interface MessageOptions {
  type?: MessageType;
  message: string;
  duration?: number;
  showClose?: boolean;
  onClose?: () => void;
  centered?: boolean;
}

export interface MessageBoxOptions {
  type?: MessageType;
  message?: string;
  title?: string;
  content?: string;
  showCancelButton?: boolean;
  confirmButtonText?: string;
  cancelButtonText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  onClose?: () => void;
  duration?: number;
  showClose?: boolean;
}

export const MessageColorMap = {
  [MessageType.INFO]: '#409EFF',
  [MessageType.SUCCESS]: '#67C23A',
  [MessageType.WARNING]: '#E6A23C',
  [MessageType.ERROR]: '#F56C6C',
}
