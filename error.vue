<template>
  <div class="error-container">
    <h1>{{ error?.statusCode }}</h1>
    <h2>{{ errorMessage }}</h2>
    <p>{{ error?.description || '抱歉，页面发生错误' }}</p>
    <button class="back-button" @click="goToHome">返回首页</button>
  </div>
  
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  error: {
    statusCode?: number;
    description?: string;
  };
}>()

const errorMessage = computed(() => {
  switch (props.error?.statusCode) {
    case 404:
      return '页面不存在'
    case 403:
      return '无权限访问'
    case 500:
      return '服务器错误'
    default:
      return '未知错误'
  }
})

const goToHome = () => {
  clearError({ redirect: '/' })
}
</script>

<style scoped>
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: 0 20px;
}

h1 {
  font-size: 8rem;
  margin-bottom: 20px;
  color: #e53935;
}

h2 {
  font-size: 2rem;
  margin-bottom: 20px;
}

p {
  margin-bottom: 30px;
  color: #666;
}

.back-button {
  padding: 12px 24px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.back-button:hover {
  background-color: #1565c0;
}
</style>
