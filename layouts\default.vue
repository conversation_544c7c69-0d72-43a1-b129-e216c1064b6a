<template>
  <div class="ai-default-layout">
    <AISider class="ai-default-layout-sider" />
    <div class="ai-default-layout-content">
      <keep-alive>
        <slot />
      </keep-alive>
    </div>
  </div>

  <ClientOnly>
    <el-dialog
      v-model="dialogVisible"
      class="custom-dialog"
      :style="currentDialogInfo.dialogStyle"
      :show-close="false"
      :close-on-click-modal="dialogCloseOnClickModal"
      align-center
      @close="close"
    >
      <component
        :is="currentComponent"
        v-if="currentComponent"
        :options="dialogOptions"
        @close="close"
      ></component>
    </el-dialog>

    <!-- <template #fallback>
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading...</div>
      </div>
    </template> -->
  </ClientOnly>
</template>

<script lang="ts" setup>
import { defineAsyncComponent } from 'vue'
import mitt from 'mitt'
import AISider from '@/components/ai-sider.vue'
import DIALOG_TYPE from '@/constants/global-dialog-type'
import { useAiStore } from '@/store/ai'

const aiStore = useAiStore()

const dialogVisible = ref(false)
const dialogType = ref(-1)
const dialogOptions = ref({})

mitt().on('showRootDialog', (payload: { type: number; options: any }) => {
  const { type, options } = payload
  if (dialogVisible.value) return

  dialogType.value = type
  dialogOptions.value = options
  dialogVisible.value = true
})
const close = () => {
  dialogVisible.value = false
  dialogType.value = -1
  dialogOptions.value = {}
}

const DialogBusinessContact = defineAsyncComponent(() => import('@/components/dialog/business-contact.vue'))
const DialogConsultation = defineAsyncComponent(() => import('@/components/dialog/consultation.vue'))
const DialogCompleteInformation = defineAsyncComponent(() => import('@/components/dialog/complete-information.vue'))

const dialogInfo = {
  [DIALOG_TYPE.BUSINESS_CONTACT]: {
    width: '568px',
    component: DialogBusinessContact,
    closeOnClickModal: false,
    dialogStyle: {
      borderRadius: '15px',
      overflow: 'hidden',
    },
  },
  [DIALOG_TYPE.CONSULTATION]: {
    component: DialogConsultation,
    closeOnClickModal: true,
    dialogStyle: {
      borderRadius: '40px',
      overflow: 'hidden',
      padding: '0',
      background: '#ffffff',
    },
  },
  [DIALOG_TYPE.COMPLETE_INFO]: {
    component: DialogCompleteInformation,
    closeOnClickModal: true,
    dialogStyle: {
      borderRadius: '16px',
      overflow: 'hidden',
      padding: '0',
      background: '#ffffff',
      transform: 'translateY(-10vh)',
    },
  },
}
const currentDialogInfo = computed(() => dialogInfo[dialogType.value] || {})
const currentComponent = computed(
  () => dialogInfo[dialogType.value]?.component || null,
)
const dialogCloseOnClickModal = computed(
  () => dialogInfo[dialogType.value]?.closeOnClickModal || false,
)
</script>


<style lang="scss">
@use '@/assets/scss/index.scss';

.ai-default-layout {
  width: 100vw;
  height: 100vh;
  min-width: 1200px;
  display: flex;
  .ai-default-layout-sider {
    flex-shrink: 0;
  }
  .ai-default-layout-content {
    flex: 1;
  }
}
</style>
