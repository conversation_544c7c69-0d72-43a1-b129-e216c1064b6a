// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'node:url'
import devEnv from './env/env.dev.ts'
import proEnv from './env/env.pro.ts'

const env = process.env.NODE_ENV === 'development' ? devEnv : proEnv

export default defineNuxtConfig({
  devtools: { enabled: false },
  devServer: {
    port: 3003,
    host: '127.0.0.1',
  },
  // 确保为 SPA 或者 Universal 模式正确构建客户端资源
  ssr: true,
  app: {
    head: {
      title: 'Plasdata',
      titleTemplate: 'Materials Search & Materials Case Service Platform | %s',
    },
  },
  modules: ['@element-plus/nuxt', '@pinia/nuxt'],
  // 配置静态资源处理
  build: {
    // 确保资源文件在构建时被正确处理
    transpile: [],
  },
  // 配置静态资源
  dir: {
    assets: 'assets',
    public: 'public',
  },
  nitro: {
    // Preview 模式下的 CORS 配置
    experimental: {
      wasm: true,
    },
    // Preview模式特殊配置 - 确保静态资源也支持CORS
    serveStatic: true,
    // 启用压缩以减少传输大小
    compressPublicAssets: true,
    // 该配置用于服务端请求转发
    routeRules: {
      // '/': { redirect: '/chat' },
      // '/.well-known/**': { redirect: '/' },
      // 优先处理静态资源路由 - 无界微前端兼容配置
      '/_nuxt/**': {
        headers: {
          'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
          'Access-Control-Allow-Headers': '*',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Max-Age': '86400',
        },
        cors: true,
      },
      ...env.proxy,
      '/**': {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods':
            'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD',
          'Access-Control-Allow-Headers': '*',
          'Access-Control-Allow-Credentials': 'false',
          'Access-Control-Max-Age': '86400',
        },
        cors: true,
      },
    },
  },
  plugins: [
    { src: '~/plugins/init.ts' },
    { src: '~/plugins/wujie.ts', mode: 'client' },
    { src: '~/plugins/nprogress.ts', mode: 'client' },
    { src: '~/plugins/message.ts', mode: 'client' },
    { src: '~/plugins/clickoutside.ts' },
  ],
  experimental: {
    externalVue: false,
  },
  vite: {
    // 开发服务器CORS配置 - 解决dev模式下无界微前端跨域问题
    server: {
      cors: {
        origin: (origin, callback) => {
          // 允许plasdata.com域名及其子域名、本地开发环境的跨域请求
          if (!origin) {
            // 无origin的请求（如同源请求）直接允许
            callback(null as any, true)
            return
          }

          // 检查是否为允许的域名
          const isAllowed =
            // plasdata.com及其子域名
            /^https?:\/\/([a-z0-9-]+\.)*plasdata\.com(:[0-9]+)?$/i.test(
              origin,
            ) ||
            // localhost (任意端口)
            /^https?:\/\/localhost(:[0-9]+)?$/i.test(origin) ||
            // 127.0.0.1 (任意端口)
            /^https?:\/\/127\.0\.0\.1(:[0-9]+)?$/i.test(origin)

          callback(null as any, isAllowed)
        },
        credentials: true, // 支持凭据传递
        methods: ['GET', 'HEAD', 'OPTIONS', 'POST', 'PUT', 'DELETE', 'PATCH'],
        allowedHeaders: ['*'],
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/assets/scss/index.scss" as *;',
        },
      },
    },

    // 静态资源处理
    assetsInclude: [
      '**/*.svg',
      '**/*.png',
      '**/*.jpg',
      '**/*.jpeg',
      '**/*.gif',
      '**/*.webp',
    ],
    // 确保 assets 目录中的文件能被正确处理
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('.', import.meta.url)),
        '~': fileURLToPath(new URL('.', import.meta.url)),
        assets: fileURLToPath(new URL('./assets', import.meta.url)),
      },
    },
    // 强制优化依赖包含 assets 文件
    optimizeDeps: {
      include: [],
      exclude: [],
    },
    // 预览模式配置
    preview: {
      port: 3003,
      host: '127.0.0.1',
    },
    // 优化微前端环境下的构建，减少CSS文件数量
    build: {
      cssCodeSplit: false, // 禁用 CSS 代码分割，将所有CSS打包到一个文件
      // 启用CSS压缩
      cssMinify: 'esbuild',
      // 设置合理的chunk大小警告阈值
      chunkSizeWarningLimit: 1000,
    },
  },
})
