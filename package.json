{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxi build", "dev": "nuxi dev", "generate": "nuxi generate", "preview": "cross-env NODE_ENV=production PORT=3003 NITRO_PORT=3003 NITRO_HOST=0.0.0.0 NITRO_PRESET=node-server nuxi preview", "postinstall": "nuxi prepare", "lint": "eslint . --ext .js,.ts,.vue", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@element-plus/nuxt": "^1.0.9", "@nuxt/image": "^1.1.0", "@nuxtjs/stylelint-module": "^5.1.0", "@types/node": "^22.15.14", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "cross-env": "^7.0.3", "element-plus": "^2.7.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-vue": "^9.20.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "nuxt": "^3.8.2", "prettier": "^2.8.8", "prettier-eslint": "^16.3.0", "sass": "1.70.0", "typescript": "^4.9.5", "vue": "^3.4.20", "vue-eslint-parser": "^9.4.2", "vue-router": "^4.2.5"}, "lint-staged": {"*.{js,ts,vue}": ["eslint --cache --fix"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@microsoft/fetch-event-source": "^2.0.1", "@pinia/nuxt": "^0.5.1", "axios": "0.21.1", "js-cookie": "3.0.0", "js-md5": "^0.8.3", "lodash-es": "^4.17.21", "md-editor-v3": "^5.5.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "qs": "6.10.1", "simplebar": "^5.3.9", "simplebar-vue": "^2.4.0", "tua-body-scroll-lock": "^1.5.0", "uuid": "8.3.2"}}