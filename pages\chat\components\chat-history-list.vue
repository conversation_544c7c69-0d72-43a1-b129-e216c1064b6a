<template>
  <div class="session-list" @click="handleLinkBtnClick">
    <div v-for="(item, index) in renderMessageList" :key="index">
      <div v-if="item.role === ROLE.USER" class="user-session">
        <div class="content">
          <div>{{ item.content }}</div>
          <el-image
            v-for="i in item.img_urls"
            :key="i"
            class="image"
            :src="i"
            :preview-src-list="[i]"
            :preview-teleported="true"
          />
        </div>
      </div>
      <div
        v-else-if="item.loading || item.reasoning || item.content"
        class="ai-session-part"
        :class="{ 'is-done': item.isDone }"
      >
        <div class="answer-block">
          <div v-if="item.loading" class="loading-content">
            <span>Thinking</span>
            <span v-for="i in 3" :key="i" class="loading-dot"></span>
          </div>
          <template v-else>
            <!-- 知识库 knowledge-->
            <div
              v-if="item.data.knowledge?.length"
              class="knowledge-content"
              :class="{ 'show-knowledge': item.showKnowledge }"
            >
              <div class="label" @click="changeKnowledge(item)">
                Response references
                {{ item.data.knowledge.length }} knowledge base entries.
                <el-icon>
                  <ArrowDown
                    :class="['arrow', { rotate: item.showKnowledge }]"
                  />
                </el-icon>
              </div>
              <div
                class="knowledge-list"
                :style="{ 'max-height': item.knowledgeHeight }"
              >
                <div
                  v-for="(f, j) in item.data.knowledge"
                  :key="j"
                  class="knowledge-item"
                >
                  <!-- <span>{{ j + 1 }}.</span> -->
                  <img class="file-icon" :src="f.fileIcon" />
                  <div class="file-name">{{ f.file }}</div>
                </div>
              </div>
            </div>

            <!-- 推理 -->
            <div class="reasoning-content">
              <MdPreview
                class="md-preview"
                editor-id="reasoning"
                :model-value="item.reasoning"
              ></MdPreview>
            </div>
            <!-- 回答 -->
            <div class="answer-content">
              <MdPreview
                class="md-preview"
                editor-id="content"
                :model-value="item.processedContent || item.content"
              ></MdPreview>
            </div>
          </template>
        </div>
        <template v-if="item.isDone">
          <!-- 自定义区域 -->
          <div v-if="item.data?.list?.length" class="material-list">
            <div class="header">
              <div class="title">
                <img src="@/assets/img/ai/file.svg" />
                <span>Related Documents</span>
              </div>
              <!-- COMPARE TODO:海外版还不支持对比 -->
              <!-- <div
                v-if="item.type === ASSISTANT_TYPE_KEY.COMPARE"
                class="more-label compare-label"
                @click="handleMoreCompare(item)"
              >
                <span>More comparison results</span>
                <el-icon><ArrowRight /></el-icon>
              </div> -->
              <!-- MATERIAL -->
              <div
                v-if="
                  item.type === ASSISTANT_TYPE_KEY.MATERIAL &&
                  item.data.total > 2
                "
                class="more-label"
                @click.stop="handleMoreMaterial(item)"
              >
                <span>More ({{ item.data.total }})</span>
                <el-icon><ArrowRight /></el-icon>
              </div>
              <!-- MATERIAL_SUBSTITUTION TODO:海外版还不支持找替代 -->
              <!-- <div
                v-if="
                  item.type === ASSISTANT_TYPE_KEY.MATERIAL_SUBSTITUTION
                "
                class="more-label"
                @click="handleSubstitution(item)"
              >
                <span>More alternatives</span>
                <el-icon><ArrowRight /></el-icon>
              </div> -->
            </div>
            <!-- 材料列表 -->
            <div class="list">
              <a
                v-for="(i, j) in item.data.list"
                :key="j"
                class="material-item"
                :href="`/datasheet/${i.id}`"
                target="_blank"
              >
                {{ i.title }}
              </a>
            </div>
          </div>
          <!-- MATERIAL -->
          <div
            v-if="item.type === ASSISTANT_TYPE_KEY.MATERIAL"
            class="material-type"
          >
            <div class="tips">Not satisfied? Try filter.</div>
            <div class="filter" @click.stop="handleFilter(item)">
              <span>Filter</span>
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
          <!-- MATERIAL_QUOTATION -->
          <div
            v-if="item.type === ASSISTANT_TYPE_KEY.MATERIAL_QUOTATION"
            class="material-quotation"
          >
            Quick quote? Contact customer service hotline:
            <span class="phone">18823327240</span>
          </div>
          <!-- 复制、重新回答、评价 -->
          <div v-if="item.id" class="general-setting">
            <div class="line-left">
              <span class="item" @click="copyText(item.content)">
                <img src="@/assets/img/ai/general-copy.svg" />
                <span>Copy</span>
              </span>
              <span class="item" @click="handleReplay(item.id)">
                <img src="@/assets/img/ai/general-replay.svg" />
                <span>Replay</span>
              </span>
            </div>
            <div class="line-right">
              <span
                class="item"
                :class="{ active: item.mark === MARK_TYPE.CORRECT }"
                @click="handleMark(item, MARK_TYPE.CORRECT)"
              >
                <img class="correct" src="@/assets/img/ai/general-icon.svg" />
                <span>Good</span>
              </span>
              <span
                class="item"
                :class="{ active: item.mark === MARK_TYPE.ERROR }"
                @click="handleMark(item, MARK_TYPE.ERROR)"
              >
                <img class="error" src="@/assets/img/ai/general-icon.svg" />
                <span>Bad</span>
              </span>
            </div>
          </div>
        </template>
      </div>
      <div v-else></div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ChatHistoryListComponent',
})
</script>

<script setup lang="ts">
import { ArrowDown, ArrowRight } from '@element-plus/icons-vue'
import { MdPreview } from 'md-editor-v3'
import { userEvaluateApi } from '@/api/ai'
import useFileTypeIcon from '@/composables/ai/useFileTypeIcon'
import { ASSISTANT_TYPE_KEY, MARK_TYPE, ROLE } from '@/types/ai/enums'
import type { MessageItem } from '@/types/ai/interface'
import { GLOBAL_PC_AI_ASK_MARK } from '@/utils/http-point/http-point-key'
import HttpPoint from '@/utils/http-point/useHttpPoint'
import 'md-editor-v3/lib/style.css'

// 链接类型定义
const LINK_TYPE = {
  PRODUCT_DETAIL: 'product-detail', // 商品详情
}

const { $SKToast } = useNuxtApp()

const props = defineProps<{
  chatHistoryList: MessageItem[];
  sessionId: string;
}>()
const emit = defineEmits<{
  (e: 'mark', data: { mark: MARK_TYPE; id: string }): void;
  (e: 'replay', id: string): void;
  (e: 'more', data: { key: string; count: number }): void;
  (e: 'replace', id: string): void;
  (e: 'filter', filter: any): void;
  (e: 'jump_compare', idList: string[]): void;
}>()

const { judgeFileType } = useFileTypeIcon()
const knowledgeMap = ref<Record<string, boolean>>({})
const changeKnowledge = (item: MessageItem) => {
  const newMap = { ...knowledgeMap.value }
  newMap[item.id] = !newMap[item.id]
  knowledgeMap.value = newMap
}

// 处理链接方法
const processLinks = (text: string) => {
  if (!text || text.length < 10) return text
  // 快速检查是否包含链接标记，如果没有直接返回
  if (!text.includes('<link')) return text

  const content = String(text)

  // 查找所有完整的链接标签
  // <link id="b0G82ALMaEq" type="product-detail">1. SABIC LEXAN™ EXL1414 PC</link>
  const LINK_REGEX =
    /<link[^>]*id="([^"]*)"[^>]*type="([^"]*)"[^>]*>([^<]*)<\/link>/g
  const completedLinks = []

  // 收集所有完整的链接
  let linkMatch
  while ((linkMatch = LINK_REGEX.exec(content)) !== null) {
    completedLinks.push({
      fullMatch: linkMatch[0],
      id: linkMatch[1],
      type: linkMatch[2],
      title: linkMatch[3],
      startIndex: linkMatch.index,
      endIndex: linkMatch.index + linkMatch[0].length,
    })
  }

  // 查找未闭合的链接标签
  const INCOMPLETE_LINK_START = /<link[^>]*(?!<\/link>)/g
  const incompleteLinks = []

  let incompleteMatch
  while ((incompleteMatch = INCOMPLETE_LINK_START.exec(content)) !== null) {
    // 检查这个开始标签是否有对应的结束标签
    const currentIndex = incompleteMatch.index
    const isCompleted = completedLinks.some(
      link => link.startIndex <= currentIndex && link.endIndex > currentIndex,
    )

    if (!isCompleted) {
      incompleteLinks.push({
        startIndex: incompleteMatch.index,
        text: incompleteMatch[0],
      })
    }
  }

  // 处理链接
  let result = content

  // 先替换完整的链接
  completedLinks.forEach(link => {
    if (link.type && link.id) {
      const replacement = `<span class="sk-ai-link-btn" data-type="${link.type}" data-id="${link.id}">${link.title}</span>`
      result = result.replace(link.fullMatch, replacement)
    } else {
      result = result.replace(link.fullMatch, link.title)
    }
  })

  // 处理未闭合的链接（隐藏它们）
  if (incompleteLinks.length > 0) {
    // 如果有未闭合的链接，只保留到第一个未闭合链接之前的内容
    const firstIncompleteIndex = Math.min(
      ...incompleteLinks.map(link => link.startIndex),
    )
    result = result.substring(0, firstIncompleteIndex)
  }

  return result
}

// 处理链接点击
const handleLinkBtnClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement
  const btn = target.closest('.sk-ai-link-btn')
  if (!btn) return

  const type = btn.getAttribute('data-type')
  const id = btn.getAttribute('data-id')

  const handlers: Record<string, () => void> = {
    // 商品-标题 - 跳转商品详情页
    [LINK_TYPE.PRODUCT_DETAIL]: () => {
      window.open(`/datasheet/${id}`, '_blank')
    },
  }

  const handler = handlers[type as string]
  if (handler) {
    handler()
  }
}

const renderMessageList = computed(() => {
  const currentList = props.chatHistoryList.map((item: MessageItem) => {
    if (item.role === ROLE.USER) return item

    // 确保knowledge存在且是数组
    const knowledge = Array.isArray(item.data?.knowledge)
      ? item.data.knowledge.map((i: any) => {
          return { ...i, fileIcon: judgeFileType(i.file).icon }
        })
      : []

    const knowledgeHeight = `${knowledge.length * 24 + 16}px`
    const showKnowledge = knowledgeMap.value[item.id] || false

    // 处理链接
    const processedContent = item.content
      ? processLinks(String(item.content))
      : ''

    return {
      ...item,
      content: String(item.content),
      processedContent,
      loading:
        !item.isDone &&
        !item.reasoning &&
        item.content.replace(/#/g, '').length < 3,
      showKnowledge,
      knowledgeHeight: showKnowledge ? knowledgeHeight : '0px',
      data: {
        ...item.data,
        knowledge,
      },
    }
  })
  return currentList.reverse()
})

const getLastTextNode = (node: any) => {
  if (node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== '') {
    return node
  }
  const children = node.childNodes
  for (let i = children.length - 1; i >= 0; i--) {
    const textNode = getLastTextNode(children[i]) as any
    if (textNode) {
      return textNode
    }
  }
  return null
}

const updateCursor = () => {
  const answerContent = document.querySelector('.answer-block') as any
  if (!answerContent) return
  const lastTextNode = getLastTextNode(answerContent)
  if (!lastTextNode || !lastTextNode.parentNode) return

  const cursorBlinks = document.querySelectorAll('.cursor-blink')
  cursorBlinks.forEach(item => {
    item.remove()
  })

  const cursorEl = document.createElement('span')
  cursorEl.classList.add('cursor-blink')
  lastTextNode.parentNode.appendChild(cursorEl)
}

watch(props.chatHistoryList, () => {
  setTimeout(() => updateCursor(), 80)
})

// 修改筛选条件
const handleFilter = (item: MessageItem) => {
  emit('filter', item.data.filter)
}

// 更多材料
const handleMoreMaterial = (item: MessageItem) => {
  emit('more', {
    key: item.data.cache_key,
    count: item.data.total,
  })
}

// // 更多对比结果 TODO:海外版还不支持对比
// const handleMoreCompare = (item: MessageItem) => {
//   const idList = item.data.list.map(
//     (i: any) => i.tds || i.tds_id || i.identify,
//   )
//   emit('jump_compare', idList)
// }

// // 更多替代 TODO:海外版还不支持找替代
// const handleSubstitution = (item: MessageItem) => {
//   emit('replace', item.data.current.id)
// }

// 重新回答
const handleReplay = (id: string) => {
  emit('replay', id)
}

// 用户评价
const handleMark = async (item: MessageItem, mark: MARK_TYPE) => {
  try {
    const eMark = item.mark === mark ? MARK_TYPE.NO_EVALUATE : mark
    await userEvaluateApi({
      mark: eMark,
      session_id: props.sessionId,
      assistant_id: item.id,
    })
    emit('mark', { mark: eMark, id: item.id })
    $SKToast.warning('Successful')
    HttpPoint({
      key_id: GLOBAL_PC_AI_ASK_MARK,
      extend_value: {
        ai_session_no: props.sessionId,
        ai_answer_no: item.id,
        ai_comment: eMark,
      },
    })
  } catch (err) {
    console.error('反馈失败:', err)
    $SKToast.error('Feedback failed, please try again')
  }
}

const copyText = async (text: string) => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text)
    } else {
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
    }
    $SKToast.warning('Successful')
  } catch (err) {
    console.error('复制失败:', err)
    $SKToast.error('Copy failed, please try again')
  }
}
</script>

<style scoped lang="scss">
.session-list {
  display: flex;
  flex-direction: column-reverse;
  height: 100%;

  .user-session {
    display: flex;
    margin: 10px 0;
    justify-content: flex-end;

    .content {
      max-width: calc(100% - 83px);
      line-height: 26px;
      padding: 8px 20px;
      border-radius: 8px;
      background: #ff7700;
      font-size: 14px;
      color: #ffffff;
      white-space: pre-wrap;
      word-break: break-word;

      .image {
        max-width: 300px;
        max-height: 500px;
      }
    }
  }
  .ai-session-part {
    display: flex;
    flex-direction: column;
    margin: 10px 0;

    .answer-block {
      line-height: 20px;
      .answer-content {
        // padding: 8px;
        margin-top: 15px;
        color: #404040;
        position: relative;

        :deep(.md-preview) {
          font-size: 14px;
          line-height: 26px;

          .sk-ai-link-btn {
            color: #3662ec;
            cursor: pointer;
            &:hover {
              text-decoration: underline;
            }

            &:not(:last-child) {
              margin-right: 12px;
            }
          }

          .compare-active {
            color: #9295ab;
            user-select: none;
            pointer-events: none;
            cursor: auto;
            &:hover {
              text-decoration: none;
            }
          }
        }
      }
      .reasoning-content {
        padding: 0 8px;
        border-left: 2px solid #eeeeee;
        color: #8b8b8b;
        :deep(.md-preview) {
          font-size: 14px;
          line-height: 26px;
        }
      }
      .loading-content {
        display: flex;
        align-items: center;
        margin: 15px 0 100px 0;
        color: #3d3d3d;

        .loading-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #ff7700;
          margin-top: 3px;
          margin-left: 5px;
          animation: loading 1.4s ease-in-out infinite;
          opacity: 0.6;
        }
        .loading-dot:nth-child(2) {
          animation-delay: 0.2s;
        }
        .loading-dot:nth-child(3) {
          animation-delay: 0.4s;
        }
        .loading-dot:nth-child(4) {
          animation-delay: 0.6s;
        }

        @keyframes loading {
          0%,
          100% {
            transform: scale(0.3);
            opacity: 0.2;
          }
          50% {
            transform: scale(1);
            opacity: 1;
          }
        }
      }
      .knowledge-content {
        transition: all 0.3s ease-in-out;
        overflow: hidden;
        margin-bottom: -8px;
        &.show-knowledge {
          .knowledge-list {
            padding: 8px 0;
            opacity: 1;
            visibility: visible;
          }
        }

        .label {
          width: fit-content;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          color: #6c6f76;
          padding: 8px 0;
          user-select: none;
          cursor: pointer;

          .el-icon {
            width: auto;
            height: auto;
            margin-bottom: -3px;
            .arrow {
              margin-left: 8px;
              transition: transform 0.3s ease;
              width: 14px;
              height: 14px;
              &.rotate {
                transform: rotate(180deg);
              }
            }
          }
        }

        .knowledge-list {
          padding: 0;
          opacity: 0;
          visibility: hidden;
          display: flex;
          flex-direction: column;
          gap: 6px;
          background: #f7f8fc;
          border-radius: 6px;
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        .knowledge-item {
          padding: 0 12px;
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #6c6f76;
          user-select: none;
          line-height: 1.5;
          .file-icon {
            width: 16px;
            height: 16px;
          }
          .file-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      :deep(.md-preview) {
        padding: 0;
        font-size: 14px;
        table {
          font-size: 12px !important;
        }
        h3 {
          font-size: 16px !important;
        }
        p {
          margin-bottom: 0 !important;
        }
      }
    }

    .material-list {
      width: 100%;
      margin-top: 8px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      .header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          font-weight: 500;
          color: #171a23;
          user-select: none;
          img {
            width: 16px;
            height: 16px;
          }
        }
        .more-label {
          width: fit-content;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          color: #6c6f76;
          flex-shrink: 0;
          user-select: none;
          cursor: pointer;
          transition: all 0.2s ease;
          &:hover {
            text-decoration: underline;
            color: darken(#6c6f76, 10%);
          }
          &:active {
            color: darken(#171a23, 10%);
          }
        }
        .compare-label {
          color: #3662ec;
          &:hover {
            color: darken(#3662ec, 10%);
          }
        }
      }

      .list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        .material-item {
          width: 100%;
          height: 32px;
          padding: 0 8px;
          line-height: 32px;
          background: #f7f8fc;
          border-radius: 8px;
          font-size: 12px;
          color: #6c6f76;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          box-sizing: border-box;
          user-select: none;
          cursor: pointer;
          transition: all 0.2s ease;
          &:hover {
            background: darken(#f7f8fc, 3%);
            transform: translateY(-1px);
          }
          &:active {
            background: darken(#f7f8fc, 5%);
            transform: translateY(0);
          }
        }
      }
    }

    .material-type {
      margin-top: 5px;
      display: flex;
      align-items: center;
      font-size: 12px;
      .tips {
        color: #9295ab;
        user-select: none;
        margin-right: 16px;
      }
      .filter {
        width: fit-content;
        height: 32px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 8px;
        color: #2259b9;
        flex-shrink: 0;
        user-select: none;
        cursor: pointer;
        transition: all 0.2s ease;
        &:hover {
          text-decoration: underline;
        }
        &:active {
          color: darken(#2259b9, 10%);
        }
      }
    }

    .material-quotation {
      margin-top: 8px;
      color: #9295ab;
      font-size: 14px;
      .phone {
        color: #3662ec;
      }
    }

    .general-setting {
      width: 100%;
      height: 38px;
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #eeeeee;
      .line-left,
      .line-right {
        display: flex;
        align-items: center;
        gap: 36px;
        .item {
          padding: 2px 4px;
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #6c6f76;
          border-radius: 4px;
          cursor: pointer;
          user-select: none;
          transition: all 0.15s ease;
          overflow: hidden;
          img {
            width: 18px;
            height: 18px;
          }
          .error {
            transform: rotate(180deg) translateY(-1px);
          }
          &:hover {
            background: #efefef;
          }
          &:active {
            background: darken(#efefef, 5%);
          }
          &.active {
            color: #ff7700;
            img.correct {
              filter: drop-shadow(#ff7700 0 100px);
              transform: translateY(-100px);
            }
            img.error {
              filter: drop-shadow(#ff7700 0 99px);
              transform: rotate(180deg) translateY(-100px);
            }
          }
        }
      }
    }
  }

  :deep(.md-preview) {
    .cursor-blink {
      display: inline-block;
      background-color: #171a23;
      animation: blink 0.8s infinite;
      vertical-align: middle;
      width: 2px;
      height: 18px;
      margin-left: 3px;
      margin-bottom: 2px;
    }

    @keyframes blink {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0;
      }
    }
  }

  .is-done {
    :deep(.md-preview) {
      .cursor-blink {
        display: none;
      }
    }
  }
}
</style>

<style lang="scss">
.sk-toast {
  top: 35vh !important;
  min-width: auto;
  width: fit-content;
  height: 56px;
  border-radius: 6px;
  background: #3d3d3d;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;

  .el-message__icon {
    display: none;
  }
  .el-message__content {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    color: #ffffff;
  }
}
</style>
