<template>
  <div class="chat-module">
    <div class="chat-part">
      <div
        v-loading="loading"
        class="session-container"
        :class="{ 'active-chat': isActiveChat }"
      >
        <SimpleBar data-simplebar-auto-hide="true" class="session-list">
          <chatHistoryListComponent
            :chat-history-list="chatHistoryList"
            :session-id="aiStore.currentSessionId"
            @mark="handleMark"
            @replay="handleReplay"
            @replace="handleReplace"
            @jump_compare="handleJumpCompare"
            @filter="openFilterDrawer"
            @more="openMoreDrawer"
          />
          <div
            v-show="props.keepActive && !chatHistoryList.length"
            class="knowledge-tips"
          >
            You can learn about relevant content in the knowledge base by asking
            questions
          </div>
        </SimpleBar>
      </div>
      <form class="input-container" @submit.prevent="sendQuestion()">
        <div v-if="newChatBtn" class="new-chat-btn" @click="onNewChat">
          New Chat
        </div>
        <el-input
          ref="inputRef"
          v-model="userQuestion"
          resize="none"
          type="textarea"
          placeholder="Sign in to experience AI-powered material selection"
          @keydown.enter.prevent="sendQuestion()"
        />
        <div class="action-bar">
          <!-- 模型/知识库 -->
          <el-select
            v-model="activeModel"
            popper-class="select-popper"
            placement="top-start"
            :offset="8"
            :show-arrow="false"
            :teleported="false"
          >
            <el-option
              v-for="item in aiStore.modelList"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            >
              <div class="option-item model-option">
                <div class="option-item-content">
                  <span class="option-item-title">{{ item.name }}</span>
                  <span class="option-item-desc">{{ item.desc }}</span>
                </div>
                <div class="option-item-checkbox"></div>
              </div>
            </el-option>
          </el-select>
          <el-select
            v-model="activeKnowledgeIds"
            popper-class="select-popper"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :offset="8"
            :show-arrow="false"
            :teleported="false"
            placeholder="No data"
          >
            <template #header>
              <div
                class="option-item knowledge-option"
                :class="{
                  'is-selected':
                    activeKnowledgeIds.length === aiStore.mineKnowledges.length,
                }"
                @click="handleSelectAll"
              >
                <div class="option-item-content">
                  <span class="option-item-title">all knowledge</span>
                </div>
                <div class="option-item-checkbox multiple-checkbox"></div>
              </div>
            </template>
            <el-option
              v-for="item in aiStore.mineKnowledges"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <div class="option-item knowledge-option">
                <div class="option-item-content">
                  <span class="option-item-title">{{ item.name }}</span>
                </div>
                <div class="option-item-checkbox multiple-checkbox"></div>
              </div>
            </el-option>
          </el-select>
          <!-- 语言 -->
          <div class="language" @click="handleLanguage">
            <el-tooltip
              effect="dark"
              content="Toggle answer language (ZH/EN)"
              placement="top"
            >
              <img
                v-if="activeLanguage === LANG.ZH.value"
                src="@/assets/img/ai/zh-icon.svg"
              />
              <img v-else src="@/assets/img/ai/en-icon.svg" />
            </el-tooltip>
          </div>

          <!-- 上传图片 -->
          <div
            v-show="activeUploadMode.includes(UPLOAD_MODE.IMAGE)"
            class="upload-img"
          >
            <el-tooltip
              effect="dark"
              content="lmage size within 10 MB"
              placement="
              top"
            >
              <el-upload
                class="cover-upload"
                :limit="1"
                :auto-upload="false"
                :file-list="imageBase.fileList"
                :show-file-list="false"
                :on-change="handleCoverChange"
                :on-exceed="handleCoverExceed"
                accept="image/jpeg,image/png,image/jpg"
              >
                <template #trigger>
                  <div class="cover-upload-trigger">
                    <img v-if="imageBase.imageUrl" :src="imageBase.imageUrl" />
                    <img
                      v-else
                      src="@/assets/img/ai/knowledge/upload-icon.svg"
                    />
                  </div>
                </template>
              </el-upload>
            </el-tooltip>
          </div>

          <!-- 发送/停止 -->
          <div
            v-if="!isWriting"
            class="send-btn"
            @click.prevent="sendQuestion()"
          >
            <img src="@/assets/img/ai/send-icon.svg" />
          </div>
          <div v-else class="stop-btn" @click.prevent="onStop">
            <img src="@/assets/img/ai/stop-icon.svg" />
          </div>
        </div>
      </form>
    </div>

    <div class="drawer-part" :class="{ 'active-drawer': isDrawerVisible }">
      <component
        :is="drawerComponent"
        :filter-data="filterData"
        :more-key="moreKey"
        :more-count="moreCount"
        @close="closeDrawer"
        @submit="handleFilterSubmit"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  fetchEventSource,
  type FetchEventSourceInit,
} from '@microsoft/fetch-event-source'
import type { ElInput } from 'element-plus'
import type { FormInstance, UploadFile, UploadUserFile } from 'element-plus'
import mitt from 'mitt'
import SimpleBar from 'simplebar-vue'
import FilterDrawer from '@//components/filter-drawer.vue'
import MoreDrawer from '@//components/more-drawer.vue'
import { getChatHistoryApi } from '@/api/ai'
import urls from '@/api/urls'
import { useLocalStorage } from '@/composables/useLocalStorage/index.js'
import { LANG } from '@/constants/basics/index.ts'
import DIALOG_TYPE from '@/constants/global-dialog-type'
import chatHistoryListComponent from '@/pages/chat/components/chat-history-list.vue'
import { useAiStore } from '@/store/ai'
import { useUserStore } from '@/store/user'
import { MARK_TYPE, ROLE, UPLOAD_MODE } from '@/types/ai/enums'
import type { KnowledgeItem, MessageItem } from '@/types/ai/interface'
import { GLOBAL_PC_AI_ASK_REPORTED } from '@/utils/http-point/http-point-key'
import HttpPoint from '@/utils/http-point/useHttpPoint'
import 'simplebar/dist/simplebar.min.css'

defineComponent({
  name: 'ChatModule',
})

const props = defineProps<{
  keepActive?: boolean;
  newChatBtn?: boolean;
}>()

const { $SKToast } = useNuxtApp()

const aiStore = useAiStore()
const userStore = useUserStore()
const { useStorage } = useLocalStorage()

const userQuestion = ref('') // 搜索内容
const nowSessionId = ref('') // 会话ID
const chatHistoryList = ref<MessageItem[]>([]) // 问答记录
const isWriting = ref(false) // 是否正在回答

const isActiveChat = computed(
  () =>
    props.keepActive ||
    !!aiStore.currentSessionId ||
    chatHistoryList.value.length > 0,
)

const activeModel = useStorage<string>(
  'activeModel',
  aiStore.modelList[0]?.name || '',
)
const activeKnowledgeIds = ref<string[]>(
  (aiStore.mineKnowledges || []).map(item => item.id),
)

watch(
  () => aiStore.mineKnowledges,
  (newList: KnowledgeItem[]) => {
    if (!activeKnowledgeIds.value.length && newList.length) {
      activeKnowledgeIds.value = newList.map((item: KnowledgeItem) => item.id)
    }
  },
  { immediate: false, deep: true },
)

const activeKnowledge = computed(() => {
  return aiStore.mineKnowledges
    .filter(item => activeKnowledgeIds.value.includes(item.id))
    .map(item => ({
      type: item.type,
      knowledge_id: item.id,
    }))
})
const handleSelectAll = () => {
  if (activeKnowledgeIds.value.length === aiStore.mineKnowledges.length) {
    activeKnowledgeIds.value = []
  } else {
    activeKnowledgeIds.value = aiStore.mineKnowledges.map(item => item.id)
  }
}
const activeLanguage = useStorage<string>('activeLanguage', LANG.ZH.value)
const handleLanguage = () => {
  activeLanguage.value =
    activeLanguage.value === LANG.ZH.value ? LANG.EN.value : LANG.ZH.value
  document.querySelector('.language')?.classList.add('rotate-animation')
  setTimeout(() => {
    document.querySelector('.language')?.classList.remove('rotate-animation')
  }, 300)
}
const activeUploadMode = computed(() => {
  const model = aiStore.modelList.find(item => item.name === activeModel.value)
  return model?.upload_mode || []
})

const imageBase = ref({
  file: null,
  base64: '',
  fileList: [],
  imageUrl: '',
})
const handleCoverChange = (uploadFile: UploadFile) => {
  const file = uploadFile.raw
  if (!file) return

  const isLessThan2M = file.size / 1024 / 1024 <= 2
  if (!isLessThan2M) {
    $SKToast.error('Cover image size must be less than 2MB!')
    return
  }

  imageBase.value.fileList = [uploadFile]
  imageBase.value.file = file
  imageBase.value.imageUrl = URL.createObjectURL(file)
  const reader = new FileReader()
  reader.onload = e => {
    imageBase.value.base64 = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const handleCoverExceed = (files: File[]) => {
  imageBase.value.fileList = []
  const uploadFile = { raw: files[0] } as UploadFile
  handleCoverChange(uploadFile)
}

const controller = ref<AbortController | null>(null)
// 建立sse连接获取回答
const startConversation = async (message: string) => {
  if (!controller.value) {
    controller.value = new AbortController()
  }
  const { signal } = controller.value
  const handlers = createEventSourceHandlers()
  const json = {
    prompt: message,
    use_knowledge: true, // 选填 是否启动知识库
    knowledge: activeKnowledge.value, // 选填 知识库
    lang: activeLanguage.value, // 选填 回复语言，默认：zh，可选：zh、en
    model: activeModel.value, // 选填 选择模型
    img_urls: [imageBase.value.base64],
    mode: 'agent', // agent、ask
    session_id: aiStore.currentSessionId,
  }
  // const formData = new FormData()
  // formData.append('prompt', message)
  // formData.append('use_knowledge', 'true')
  // formData.append('knowledge', activeKnowledge.value)
  // formData.append('lang', activeLanguage.value)
  // formData.append('model', activeModel.value)
  // formData.append('mode', 'agent')
  // formData.append('session_id', aiStore.currentSessionId)
  // if (imageBase.value.file) {
  //   await new Promise<void>(resolve => {
  //     const reader = new FileReader()
  //     reader.onload = e => {
  //       formData.append('img_urls', e.target?.result as string)
  //       resolve()
  //     }
  //     reader.readAsDataURL(imageBase.value.file as File)
  //   })
  // }

  const url = `/sokoo${urls.ai_v1.chat}`
  const params: FetchEventSourceInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'text/event-stream',
    },
    body: JSON.stringify(json),
    signal,
    openWhenHidden: true,
    ...handlers,
  }

  fetchEventSource(url, params)
}
// 重新生成
const handleReplayRequest = (data: any) => {
  if (!controller.value) {
    controller.value = new AbortController()
  }
  const { signal } = controller.value
  const handlers = createEventSourceHandlers()
  const json = {
    ...data,
    use_knowledge: true, // 选填 是否启动知识库
    knowledge: activeKnowledge.value, // 选填 知识库
    lang: activeLanguage.value, // 选填 回复语言，默认：zh，可选：zh、en
    model: activeModel.value, // 选填 选择模型
    img_urls: [imageBase.value.base64],
    mode: 'agent', // agent、ask
  }
  const url = `/sokoo${urls.ai_v1.chat_reply}`
  const params: FetchEventSourceInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(json),
    signal,
    openWhenHidden: true,
    ...handlers,
  }
  fetchEventSource(url, params)
}
// 创建sse连接回调
const createEventSourceHandlers = () => {
  return {
    onopen: async (e: any) => {
      e.reconnectInterval = 0
      return handleConnectionOpen(e)
    },
    onmessage: (msg: any) => handleMessage(msg),
    onclose: () => handleConnectionClose(),
    onerror: (err: any) => handleConnectionError(err),
  }
}
const handleConnectionOpen = async (e: any) => {
  try {
    if (!e.headers.get('content-type').includes('text/event-stream')) {
      throw new Error('Service unavailable. Please retry later.。')
    }
    const sessionId = e.headers.get('x-session-id')
    if (sessionId) {
      nowSessionId.value = sessionId
      aiStore.setCurrentSessionId(sessionId)
    }
  } catch (error: any) {
    console.error('打开连接失败', error)
    updateMessage({ content: error.message || '连接服务器失败' })
  }
}
const handleMessage = (msg: any) => {
  try {
    if (msg.data === '[DONE]') {
      updateMessage({ isDone: true })
      return
    }
    const res = JSON.parse(msg.data)
    const content = extractMessageContent(res)
    updateMessage(content)
    scrollToBottom()
  } catch (error: any) {
    console.error('处理消息失败', error)
    updateMessage({
      content: `\nService unavailable. Please retry later.。`,
      isDone: true,
    })
    controller.value?.abort()
    controller.value = null
    isWriting.value = false
  }
}
const handleConnectionClose = () => {
  mitt().emit('ai-sider-refresh-history')
  updateMessage({ isDone: true })
  isWriting.value = false
  scrollToBottom(true)
  aiStore.setCurrentSessionId(nowSessionId.value)
}
const handleConnectionError = (err: any) => {
  controller.value?.abort()
  controller.value = null
  console.error('连接错误', err)
  updateMessage({
    content: `\nService unavailable. Please retry later.。`,
    isDone: true,
  })
  isWriting.value = false
  aiStore.setCurrentSessionId(nowSessionId.value)
}
// 格式化消息
const extractMessageContent = (res: MessageItem) => {
  if (!res) return {}

  const messageInfo = {
    id: '',
    content: '',
    reasoning: '',
    type: '',
    data: {
      knowledge: [] as KnowledgeItem[],
    },
    created: '',
  }

  // 处理assistant_id情况
  if (res.id) {
    const { type, data, id } = res

    messageInfo.id = id

    // 更新chatHistoryList中的id
    const lastMessage = chatHistoryList.value[chatHistoryList.value.length - 1]
    if (lastMessage && !lastMessage.id) {
      lastMessage.id = id
    }

    if (data) messageInfo.data = { ...messageInfo.data, ...data }
    if (type) messageInfo.type = type

    // 处理knowledge
    if (data?.knowledge && Array.isArray(data.knowledge)) {
      const knowledgeMap = new Map<string, KnowledgeItem>()
      data.knowledge.forEach((item: KnowledgeItem) => {
        if (!knowledgeMap.has(item.file)) {
          knowledgeMap.set(item.file, item)
        }
      })
      messageInfo.data.knowledge = Array.from(knowledgeMap.values())
    }
  }

  // 处理常规消息内容
  if (res.choices?.[0]?.delta) {
    const { delta } = res.choices[0]
    if (delta.id) messageInfo.id = delta.id
    if (delta.content) messageInfo.content = delta.content
    if (delta.reasoning_content) messageInfo.reasoning = delta.reasoning_content

    // 处理创建时间
    if (res.created) messageInfo.created = res.created
  }

  return messageInfo
}
const updateMessage = (data: Partial<MessageItem>) => {
  if (!chatHistoryList.value.length) return

  const { content, reasoning, data: dataItem, ...other } = data
  const index = chatHistoryList.value.findIndex(
    (item: MessageItem) => item.id === nowSessionId.value,
  )

  const nowAssistantIndex =
    index !== -1 ? index : chatHistoryList.value.length - 1
  const nowMessage = chatHistoryList.value[nowAssistantIndex]

  if (!nowMessage) return

  if (content) {
    nowMessage.content = (nowMessage.content || '') + content
  }
  if (reasoning) {
    nowMessage.reasoning = (nowMessage.reasoning || '') + reasoning
  }
  if (dataItem) {
    // 确保knowledge数组被正确合并
    const mergedData = { ...nowMessage.data, ...dataItem }

    // 特殊处理knowledge数组
    if (dataItem.knowledge && Array.isArray(dataItem.knowledge)) {
      const existingKnowledge = nowMessage.data.knowledge || []
      const knowledgeMap = new Map<string, KnowledgeItem>()

      // 合并现有和新的knowledge项
      existingKnowledge
        .concat(dataItem.knowledge)
        .forEach((item: KnowledgeItem) => {
          if (item.file) {
            knowledgeMap.set(item.file, item)
          }
        })

      mergedData.knowledge = Array.from(knowledgeMap.values())
    }

    nowMessage.data = mergedData
  }
  if (Object.keys(other).length > 0) {
    Object.assign(nowMessage, other)
  }
}
const scrollToBottom = (force = false) => {
  const container = document.querySelector('.simplebar-content-wrapper')
  if (!container) return

  const extraHeight = 200
  const isAtBottom =
    container.scrollHeight - container.scrollTop <=
    container.clientHeight + extraHeight

  if (isAtBottom || force) {
    // 确保scrollHeight计算准确并使用更大倍数确保滚到底部
    const scrollPosition = container.scrollHeight * 3
    container.scrollTo({
      top: scrollPosition,
      behavior: force ? 'auto' : 'smooth',
    })
  }
}
const inputRef = ref<InstanceType<typeof ElInput> | null>(null)
const sendQuestion = (message?: string) => {
  if (!isLogin.value) {
    handleLogin()
    return
  }
  const question = message || userQuestion.value
  if (isWriting.value || !question) return

  // 添加用户提问
  chatHistoryList.value.push({
    role: ROLE.USER,
    content: question,
    data: {},
    img_urls: imageBase.value.imageUrl ? [imageBase.value.imageUrl] : [],
  })

  // 添加AI回答
  chatHistoryList.value.push({
    role: ROLE.AI,
    content: '',
    reasoning: '',
    data: {},
    id: '',
    created: '',
    mark: MARK_TYPE.NO_EVALUATE,
    isDone: false,
  })

  scrollToBottom(true)

  // 建立sse连接获取回答
  startConversation(question)
  isWriting.value = true

  // 重置状态
  nowSessionId.value = ''
  userQuestion.value = ''
  inputRef.value?.blur()
  imageBase.value = {
    file: null,
    base64: '',
    fileList: [],
    imageUrl: '',
  }
}
// 停止对话
const onStop = () => {
  controller.value?.abort()
  controller.value = null
  updateMessage({ isDone: true })
  isWriting.value = false
  reportConversation()
}
// 埋点上报
const reportConversation = () => {
  const sessionIndex = nowSessionId.value
    ? chatHistoryList.value.findIndex(
        (item: MessageItem) => item.id === nowSessionId.value,
      )
    : chatHistoryList.value.length - 1
  if (sessionIndex === -1) return

  const question = chatHistoryList.value[sessionIndex - 1]
  const answer = chatHistoryList.value[sessionIndex]

  const extendValue = {
    ai_session_no: aiStore.currentSessionId,
    ai_answer_no: nowSessionId.value || answer.id || '',
    ai_model: activeModel.value || '',
    ai_answer_type: answer.data?.type || '',
    ai_answer_content: answer.content || '',
    ai_answer_time: answer.created || new Date().getTime(),
    ai_question_content: question.content,
    ai_first_question: Number(!(sessionIndex - 1)),
  }

  if (answer.data?.list?.length) {
    extendValue.ai_answer_content += '\n\n相关材料：\n'
    answer.data.list.forEach((i: any) => {
      extendValue.ai_answer_content += `${i.title}\n`
    })
  }

  HttpPoint({
    key_id: GLOBAL_PC_AI_ASK_REPORTED,
    extend_value: extendValue,
  })
}

const isLogin = computed(() => userStore.isLogin)
const handleLogin = () => {
  mitt().emit('showRootDialog', {
    type: DIALOG_TYPE.LOGIN,
  })
}

watch(
  () => aiStore.currentSessionId,
  (newId: string) => {
    if (!newId) {
      nowSessionId.value = ''
      onNewChat()
      return
    }
    if (newId !== nowSessionId.value) {
      loadChatHistoryList(newId)
    }
    nowSessionId.value = newId
  },
)
// 新对话
const onNewChat = () => {
  if (!isLogin.value) {
    handleLogin()
    return
  }
  onStop()
  chatHistoryList.value = []
  nowSessionId.value = ''
  aiStore.setCurrentSessionId('')
  nextTick(() => {
    setTimeout(() => {
      inputRef.value?.focus()
    }, 100)
  })
}

// 加载历史消息
const loading = ref(false)
const loadChatHistoryList = async (id: string) => {
  loading.value = true
  try {
    const { data } = await getChatHistoryApi({ session_id: id })

    if (!data || !Array.isArray(data)) {
      throw new Error('历史消息数据格式错误')
    }
    chatHistoryList.value = data.reduce((acc, item) => {
      if (!item.visible) return acc
      const { footer, header, ...other } = item
      return [
        ...acc,
        {
          ...other,
          isDone: true,
          type: header.type || footer.type,
          data: { ...footer.data, ...header.data },
        },
      ]
    }, [])

    nextTick(() => {
      setTimeout(() => {
        scrollToBottom(true)
        loading.value = false
      }, 0)
    })
  } catch (error) {
    console.error('加载历史消息失败:', error)
    loading.value = false
  }
}
// 用户评价
const handleMark = (params: { mark: MARK_TYPE; id: string }) => {
  const { mark, id } = params
  const message = chatHistoryList.value.find(
    (item: MessageItem) => item.id === id,
  )
  if (message) {
    message.mark = mark
  }
}
// 重新回答
const handleReplay = (id: string) => {
  if (isWriting.value) return

  isWriting.value = true
  nowSessionId.value = id
  const nowSessionIndex = chatHistoryList.value.findIndex(
    (item: MessageItem) => item.id === id,
  )
  if (nowSessionIndex !== -1) {
    chatHistoryList.value.splice(
      nowSessionIndex,
      chatHistoryList.value.length - 1,
    )
    chatHistoryList.value.push({
      role: ROLE.AI,
      content: '',
      reasoning: '',
      data: {},
      id: id,
      isDone: false,
    })

    handleReplayRequest({
      session_id: aiStore.currentSessionId,
      assistant_id: id,
    })
  }
}
// 找替代
const handleReplace = (id: string) => {
  window.open(`/ds/${id}/replace`, '_blank')
}

// 跳转对比
const handleJumpCompare = (idList: string[]) => {
  if (!Array.isArray(idList) || !idList.length) return
  window.open(`/ds/comparison/${idList.join('-')}`, '_blank')
}

// 筛选弹窗、更多材料弹窗
const isDrawerVisible = ref(false)
const filterData = ref<any>(null)
const drawerComponent = ref<Component | null>(null)

// 打开筛选弹窗
const openFilterDrawer = (filter: any) => {
  filterData.value = filter
  drawerComponent.value = markRaw(FilterDrawer)
  isDrawerVisible.value = true
}

// 筛选弹窗提交
const handleFilterSubmit = (filterMessage: string) => {
  sendQuestion(filterMessage)
  closeDrawer()
}

// 打开更多材料弹窗
const moreKey = ref('')
const moreCount = ref(0)
const openMoreDrawer = (data: { key: string; count: number }) => {
  moreKey.value = data.key
  moreCount.value = data.count
  drawerComponent.value = markRaw(MoreDrawer)
  isDrawerVisible.value = true
}

// 关闭弹窗
const closeDrawer = () => {
  isDrawerVisible.value = false
  setTimeout(() => {
    drawerComponent.value = null
    filterData.value = null
    moreKey.value = ''
    moreCount.value = 0
  }, 300)
}

// 导出供父组件访问的属性
defineExpose({
  chatHistoryList,
  sendQuestion,
  onStop,
  handleReplay,
})
</script>

<style lang="scss" scoped>
.chat-module {
  width: 100%;
  display: flex;
  justify-content: center;
  container-type: inline-size;

  .chat-part {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;

    .session-container {
      width: 800px;
      height: 0;
      transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      will-change: transition;

      :deep(.simplebar-track) {
        right: -12px;
      }

      .session-list {
        height: 100%;
        padding-bottom: 25px;
      }

      .knowledge-tips {
        margin-top: 35%;
        font-size: 16px;
        color: #6c6f76;
        text-align: center;
        user-select: none;
      }

      &.active-chat {
        height: calc(100% - 160px);
      }
    }

    .input-container {
      width: 800px;
      height: 140px;
      display: flex;
      flex-direction: column;
      z-index: 2;
      background: #f7f8fa;
      box-shadow: 0 -10px 20px rgba(255, 255, 255, 0.9);
      border-radius: 16px;

      :deep(.el-textarea__inner) {
        height: 90px;
        padding: 11px 16px 50px 16px;
        background: transparent;
        border: none;
        box-shadow: none;
        box-sizing: border-box;
      }

      position: relative;
      .new-chat-btn {
        width: 110px;
        height: 32px;
        position: absolute;
        left: 0;
        top: -42px;
        font-size: 12px;
        color: #171a23;
        border-radius: 6px;
        background: #ffffff;
        border: 1px solid #eeeeee;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: transform;
        &:hover {
          background: #eeeeee;
        }
        &:active {
          background: darken(#eeeeee, 10%);
        }
      }

      .action-bar {
        width: 100%;
        height: 50px;
        padding: 8px 16px;
        box-sizing: border-box;
        display: flex;
        align-items: center;

        .el-select {
          width: 125px;
          margin-right: 16px;
        }
        :deep(.el-select__wrapper) {
          border-radius: 6px;
          border: none;
          background: #ffffff;
          box-shadow: none;
          padding: 4px 8px;
          &:hover {
            border-color: darken(#eeeeee, 5%);
            box-shadow: 0 0 0 0.5px darken(#eeeeee, 5%);
          }
          &:focus {
            border-color: darken(#eeeeee, 10%);
            box-shadow: 0 0 0 0.5px darken(#eeeeee, 10%);
          }
        }
        :deep(.el-select__selected-item) {
          font-size: 12px;
          color: #171a23;
        }
        :deep(.el-select__selection.is-near) {
          margin-left: 0;
          width: 100%;
          display: flex;
          // overflow: hidden;
          // text-overflow: ellipsis;
          // white-space: nowrap;
        }
        :deep(.el-select__selection) {
          font-size: 12px;
          color: #171a23;
          line-height: normal;
        }
        // :deep(.el-select__wrapper.is-focused) {
        //   background: #f1f3f6;
        // }

        .upload-img,
        .language {
          width: 32px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          user-select: none;
          cursor: pointer;
        }

        .language {
          img {
            width: 20px;
            height: 20px;
            will-change: transform;
          }

          border-radius: 6px;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          will-change: transition;
          &:hover {
            background: #e9e9e9;
          }
          &:active {
            background: darken(#e9e9e9, 10%);
          }

          &.rotate-animation {
            img {
              animation: rotate-animation 0.3s cubic-bezier(0.4, 0, 0.2, 1)
                forwards;
            }
          }

          @keyframes rotate-animation {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        }

        .upload-img {
          margin: 0 32px 0 auto;
          position: relative;
          img {
            width: 24px;
            height: 24px;
          }
          border-radius: 6px;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          will-change: transition;
          &:hover {
            background: #e9e9e9;
          }
          &:active {
            background: darken(#e9e9e9, 10%);
          }
          &::before {
            content: '';
            width: 2px;
            height: 11px;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 3;
            background-color: #d8d8d8;
            position: absolute;
            display: block;
            pointer-events: none;
          }

          :deep(.el-tooltip__trigger) {
            line-height: 0;
          }
        }

        .send-btn,
        .stop-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          overflow: hidden;
          user-select: none;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

          &:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          }

          &:active {
            transform: scale(0.95);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
          }

          img {
            width: 30px;
            height: 30px;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: transform;

            &:hover {
              transform: scale(1.1);
            }
          }
        }
        .stop-btn {
          background: #ffffff;
          &:hover {
            background: #eeeeee;
          }
        }
        .send-btn {
          background: #dce5ee;
          &:hover {
            background: darken(#dce5ee, 15%);
          }
        }
      }
    }
  }

  .drawer-part {
    width: 0;
    margin-top: 25px;
    box-sizing: border-box;
    flex-shrink: 0;
    opacity: 0;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity, width;
    height: calc(100% - 160px);

    &.active-drawer {
      width: 350px;
      opacity: 1;
      transform: translate(-20px, 0);
    }
  }

  @container (max-width: 1200px) {
    .drawer-part {
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(350px, 0);
    }
  }
}
</style>

<style lang="scss">
.select-popper {
  width: 240px;
  padding: 8px;
  border-radius: 8px;
  background: #ffffff;
  border: none;
  .el-scrollbar__view {
    padding: 0;
    .el-select-dropdown__item {
      padding: 0;
      height: auto;
      border-radius: 8px;
      box-sizing: border-box;
      transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      &::after {
        display: none;
      }
      &.is-selected {
        .option-item-title,
        .option-item-desc {
          font-weight: normal;
        }

        .option-item-checkbox {
          background: #ff6600;
          &::after {
            border-color: #ffffff;
            transform: rotate(45deg) scaleY(1);
          }
        }
      }
    }
  }

  .el-select-dropdown__header {
    padding: 0;
    margin-bottom: 4px;
    border: none;
    user-select: none;
    cursor: pointer;
    border-radius: 8px;
    box-sizing: border-box;
    position: relative;
    &::after {
      content: '';
      width: 100%;
      height: 1px;
      background: #f5f7fa;
      position: absolute;
      bottom: -2px;
      left: 0;
    }
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background: #f5f7fa;
    }

    .is-selected {
      .option-item-title,
      .option-item-desc {
        font-weight: normal;
      }

      .option-item-checkbox {
        background: #ff6600;
        &::after {
          border-color: #ffffff;
          transform: rotate(45deg) scaleY(1);
        }
      }
    }
  }

  .option-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;

    .option-item-content {
      display: flex;
      flex-direction: column;
      .option-item-title {
        font-size: 14px;
        color: #171a23;
        line-height: normal;
      }
      .option-item-desc {
        font-size: 12px;
        color: #9295ab;
        line-height: normal;
      }
    }

    .option-item-checkbox {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #ffffff;
      position: relative;
      transition: border 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        background-color 0.2s;

      &.multiple-checkbox {
        border-radius: 6px;
      }

      &::after {
        content: '';
        border: 2px solid transparent;
        border-left: 0;
        border-top: 0;
        height: 7px;
        left: 7px;
        top: 4px;
        transform: rotate(45deg) scaleY(0);
        width: 3px;
        transition: transform 0.15s ease-in 0.05s;
        transform-origin: center;
        box-sizing: content-box;
        position: absolute;
      }
    }
  }

  .model-option {
    height: 53px;
  }
  .knowledge-option {
    height: 40px;
  }
  .knowledge-tag {
    font-size: 12px;
    color: #171a23;
  }
}
</style>
