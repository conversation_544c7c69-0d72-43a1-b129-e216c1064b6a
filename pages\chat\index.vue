<template>
  <div class="ai-chat-panel" :class="{ 'active-chat': isActiveChat }">
    <div class="panel-header">
      <UserStatus />
    </div>

    <div class="panel-content">
      <div class="chat-bannel">
        <div class="logo-container">
          <img class="logo" src="@/assets/img/ai/ai-logo.svg" />
          <div class="title">Plasdata AI</div>
        </div>
        <p class="description">
          I can help you recommend materials, find alternatives, analyze and
          <br />
          compare, and solve various material needs for you.
        </p>
      </div>

      <div class="chat-module">
        <ChatModule ref="chatModuleRef" />
      </div>

      <div v-show="recommendKnowledges.length" class="knowledge-container">
        <div class="container-title">Knowledge Picks</div>
        <SimpleBar data-simplebar-auto-hide="true" class="simplebar-container">
          <div class="container-content">
            <knowledgeItem
              v-for="(item, index) in recommendKnowledges"
              :key="index"
              :detail="item"
            />
          </div>
        </SimpleBar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
import SimpleBar from 'simplebar-vue'
import UserStatus from '@/components/user/ai-user-status.vue'
import ChatModule from '@/pages/chat/components/chat-module.vue'
import KnowledgeItem from '@/pages/knowledge/components/knowledge-item.vue'
import { useAiStore } from '@/store/ai'
import 'simplebar/dist/simplebar.min.css'

defineComponent({ name: 'ChatPanel' })

const aiStore = useAiStore()
const recommendKnowledges = computed(() => aiStore.recommendKnowledges)

const chatModuleRef = ref<any>(null)
const isActiveChat = ref(false)
watchEffect(() => {
  isActiveChat.value =
    !!aiStore.currentSessionId ||
    (chatModuleRef.value?.chatHistoryList?.length ?? 0) > 0
})
</script>

<style scoped lang="scss">
.ai-chat-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;

  .panel-header {
    width: 100%;
    height: 64px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-shrink: 0;
    background: #ffffff;
    position: relative;
    border-bottom: 1px solid transparent;
  }

  .panel-content {
    width: 100%;
    height: calc(100% - 64px);
    margin: 0 auto;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    contain: layout;
    will-change: transform;
    contain: layout;
    // contain: layout paint style;
    // isolation: isolate;
    // backface-visibility: hidden;

    :deep(.simplebar-track) {
      right: -12px;
    }

    .chat-bannel {
      margin: 0 auto;
      height: 243px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0;
      box-sizing: border-box;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      .logo-container {
        margin-top: 100px;
        display: flex;
        justify-content: center;
        user-select: none;
        .logo {
          width: 35px;
          margin-right: 18.5px;
        }
        .title {
          font-size: 24px;
          font-weight: 500;
          line-height: normal;
          color: #171a23;
        }
      }

      .description {
        margin: 34px 0;
        font-size: 14px;
        color: #6c6f76;
      }
    }

    .chat-module {
      height: auto;
    }

    .knowledge-container {
      width: 810px;
      margin: 0 auto;
      height: calc(100vh - 530px);
      display: flex;
      flex-direction: column;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      .container-title {
        padding: 0 6px;
        margin-top: 30px;
        padding-bottom: 10px;
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: #1a1a1a;
        background: #ffffff;
      }
      .container-content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 810px;
        padding: 6px 6px 0 6px;
        gap: 16px 18px;
        padding-bottom: 20px;
      }

      .simplebar-container {
        height: calc(100vh - 530px);
      }
    }
  }

  &.active-chat {
    .chat-bannel,
    .knowledge-container {
      height: 0 !important;
      overflow: hidden !important;
      opacity: 0 !important;
    }

    .chat-module {
      height: 100% !important;
    }
  }
}
</style>
