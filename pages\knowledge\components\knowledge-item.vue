<template>
  <a :href="`/ai/knowledge/${detail.id}`" class="Knowledge-item">
    <div class="Knowledge-image">
      <img :src="detail.thumb" />
    </div>
    <div class="Knowledge-content">
      <div class="title-line">
        <div class="title">{{ detail.name }}</div>
        <div v-if="detail.default" class="default">Default</div>
      </div>
      <div class="desc">{{ detail.desc }}</div>
      <div class="info">
        <div class="info-item">
          <img src="@/assets/img/ai/knowledge/file-icon.svg" />
          <span>{{ detail.file_count }}</span>
        </div>
        <div class="info-item">
          <img src="@/assets/img/ai/knowledge/follow-icon.svg" />
          <span>{{ detail.follow_count }}</span>
        </div>
        <div class="info-item">
          <span>@{{ detail.creator }}</span>
        </div>
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
import type { KnowledgeItem } from '@/types/ai/interface'

defineComponent({
  name: 'KnowledgeItem',
})

const props = defineProps<{
  detail: KnowledgeItem
}>()
</script>

<style lang="scss" scoped>
.Knowledge-item {
  width: 390px;
  height: 109px;
  border-radius: 16px;
  display: flex;
  padding: 16px;
  background: #f7f8fa;
  box-sizing: border-box;
  user-select: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  &:hover {
    background: #ffffff;
    box-shadow: 0px 4px 16px 0px rgba(173, 190, 215, 0.4);
  }
  &:active {
    background: darken(#f7f8fa, 3%);
  }

  display: flex;
  gap: 10px;

  .Knowledge-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background: #f8f9fa;
    overflow: hidden;
    flex-shrink: 0;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .Knowledge-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    .title-line {
      margin-bottom: 8px;
      display: flex;
      gap: 8px;
      .title {
        font-size: 18px;
        font-weight: 500;
        line-height: 20px;
        color: #171a23;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .default {
        width: fit-content;
        height: 20px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6px;
        box-sizing: border-box;
        border: 1px solid #ff6600;
        font-size: 12px;
        font-weight: bold;
        line-height: normal;
        color: #ff6600;
        flex-shrink: 0;
      }
    }

    .desc {
      font-size: 13px;
      color: #9295ab;
    }
    .title,
    .desc {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .info {
      display: flex;
      gap: 10px;
      margin-top: auto;
      .info-item {
        min-width: 50px;
        display: flex;
        align-items: center;
        gap: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        img {
          width: 14px;
          height: 14px;
          object-fit: cover;
        }
        span {
          font-size: 12px;
          color: #9295ab;
        }
      }
    }
  }
}
</style>
