<template>
  <div v-if="!isError" class="share-page">
    <div class="page-title">
      {{ pageTitle }}
    </div>
    <div class="knowledge-container">
      <div class="knowledge-info">
        <div class="knowledge-info-header">
          <div class="header-left">
            <img src="" class="knowledge-image" />
            <div class="knowledge-text">
              <div class="knowledge-name">SABIC产品指南</div>
              <div class="knowledge-author">SABIC</div>
            </div>
          </div>
          <div class="join-btn">Join</div>
        </div>
        <div class="knowledge-description">
          {{ description }}
        </div>
      </div>
      <div class="knowledge-file-count">{{ fileList.length }} Content</div>
      <div class="knowledge-file-list">
        <div v-for="(item, index) in fileList" :key="index" class="file-item">
          <div class="file-name">{{ item.name }}</div>
          <div class="file-info">
            <div class="file-type">{{ item.type }}</div>
            <div class="file-time">{{ item.createdAt }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="input-container">
      <div class="tips">Ask from knowledge base</div>
      <img class="send-btn" src="@/assets/img/ai/send-icon.svg" />
    </div>
  </div>
  <div v-else class="error-page">
    <img class="empty-img" src="@/assets/img/ai/empty-img.svg" />
    <div class="error-tips">
      Sorry, this knowledge base is no longer accessible
    </div>
    <div class="button">Plasdata Al</div>
  </div>
</template>

<script setup lang="ts">

const isError = ref(false)

const pageTitle = ref(
  'Plasdata AI offers a leading materials knowledge base and AI technology',
)
const description = ref(
  'A comprehensive and professional knowledge aggregation platform, built around various products of the globally renowned diversified che... ',
)

const fileList = ref([
  {
    name: 'ULTEM™ Resin Technical and Processing White Paper',
    type: 'word',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC High-Performance Polymer Material Selection Guide (2024 Edition)',
    type: 'pdf',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'word',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'pdf',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'word',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'pdf',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'word',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'pdf',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'word',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'pdf',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'word',
    createdAt: '2025-01-01',
  },
  {
    name: 'SABIC产品指南',
    type: 'pdf',
    createdAt: '2025-01-01',
  },
])
</script>

<style scoped lang="scss">
.share-page {
  width: 800px;
  height: 100%;
  margin: 0 auto;
  padding: 32px 0;
  position: relative;
  background-color: #ffffff;

  .page-title {
    font-size: 24px;
    font-weight: 500;
    line-height: 36px;
    color: #171a23;
  }

  .knowledge-container {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #f3f5f7;

    .knowledge-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 16px;
      border-bottom: 1px solid #f3f5f7;

      .knowledge-info-header {
        width: 100%;
        padding: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-left {
          display: flex;
          align-items: center;

          .knowledge-image {
            width: 60px;
            height: 60px;
            margin-right: 16px;
            border-radius: 8px;
            box-sizing: border-box;
            border: 1px solid #eeeeee;
          }
        }
        .join-btn {
          width: 120px;
          height: 40px;
          border-radius: 4px;
          background: #ff7700;
          font-size: 14px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          user-select: none;
          cursor: pointer;
          transition: all 0.3s ease;
          &:hover {
            background: lighten(#ff7700, 10%);
          }
          &:active {
            background: darken(#ff7700, 10%);
          }
        }
      }
      .knowledge-description {
        margin-top: 16px;
        padding: 0 8px;
        font-size: 14px;
        color: #9295ab;
      }
    }
    .knowledge-file-count {
      padding: 16px 0;
      font-size: 14px;
      color: #9295ab;
    }
    .knowledge-file-list {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .file-item {
        width: 100%;
        height: 80px;
        padding: 16px;
        border-radius: 6px;
        background: #ffffff;
        box-sizing: border-box;
        border: 1px solid #eeeeee;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .file-name {
          font-size: 16px;
          line-height: 16px;
          color: #171a23;
        }
        .file-info {
          display: flex;
          align-items: center;
          gap: 24px;
          .file-type {
            font-size: 14px;
            color: #9295ab;
          }
          .file-time {
            font-size: 14px;
            color: #9295ab;
          }
        }

        cursor: pointer;
        transition: all 0.3s ease;
        &:hover {
          background: #f7f8fa;
          transform: translateY(-2px);
        }
      }
    }
  }

  .input-container {
    width: 800px;
    height: 48px;
    padding: 0 23px;
    border-radius: 8px;
    background: #ffffff;
    box-sizing: border-box;
    border: 1px solid #ff6600;
    box-shadow: 0px 3px 8px 0px rgba(255, 98, 0, 0.16);

    display: flex;
    align-items: center;
    justify-content: space-between;

    position: fixed;
    bottom: 10px;
    margin: 0 auto;
    z-index: 1000;

    .tips {
      font-size: 14px;
      font-weight: normal;
      color: #6c6f76;
    }

    .send-btn {
      width: 26px;
      height: 26px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      overflow: hidden;
      background: #dce5ee;
    }

    cursor: pointer;
    transition: all 0.3s ease;
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0px 3px 8px 0px rgba(255, 98, 0, 0.16);
    }
  }
}

.error-page {
  width: 100vw;
  height: 80vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .empty-img {
    width: 154px;
    height: 154px;
  }
  .error-tips {
    margin-top: 16px;
    font-size: 14px;
    font-weight: 350;
    text-align: center;
    color: #9295ab;
  }
  .button {
    width: 128px;
    height: 40px;
    margin-top: 40px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ff6600;
    font-size: 16px;
    color: #ffffff;

    user-select: none;
    cursor: pointer;
    transition: all 0.3s ease;
    &:hover {
      background: lighten(#ff6600, 10%);
    }
    &:active {
      background: darken(#ff6600, 10%);
    }
  }
}
</style>
