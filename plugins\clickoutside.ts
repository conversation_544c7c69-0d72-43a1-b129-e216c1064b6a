const clickOutside = {
  mounted(el: any, binding: any) {
    // 在指令绑定到元素时执行的代码
    const onClickOutside = (event: any) => {
      // 存在arg参数时，判断点击的元素是否有指定的类名
      if (binding.arg) {
        let target = event.target
        while (target) {
          if (target.classList.contains(binding.arg)) {
            return
          }
          target = target.parentElement
        }
      }

      if (!el.contains(event.target) && el !== event.target) {
        // 确保只有在鼠标点击并且点击位置在元素外部时才触发
        if (event.type === 'click') {
          binding.value()
        }
      }
    }
    // 只监听click事件，而不是所有的鼠标事件
    document.addEventListener('click', onClickOutside)
    el._clickoutside = onClickOutside
  },
  beforeUnmount(el: any) {
    // 在指令从元素上解绑时执行的代码
    document.removeEventListener('click', el._clickoutside)
    delete el._clickoutside
  },
}
export default defineNuxtPlugin(nuxtApp => {
  if (process.server) {
    nuxtApp.vueApp.directive('clickOutside', {})
    return
  }
  nuxtApp.vueApp.directive('clickOutside', clickOutside)
})
