import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 添加自定义样式覆盖默认样式
const styleElement =
  typeof document !== 'undefined' ? document.createElement('style') : null
if (styleElement) {
  styleElement.textContent = `
    #nprogress .bar {
      background-color: #ff6d00 !important;
      height: 2px;
    }
  `
  document.head.appendChild(styleElement)
}

/*配置加载*/
NProgress.configure({
  easing: 'ease-out', // 动画方式改为ease-out
  speed: 300, // 减少递增进度条的速度
  showSpinner: false, // 是否显示加载icon
  trickleSpeed: 100, // 减少自动递增间隔使动画更流畅
  minimum: 0.2, // 降低初始化时的最小百分比，让进度条增长过程更平滑
})

export default defineNuxtPlugin((): void => {
  const router = useRouter()
  router.beforeEach(
    (
      to: RouteLocationNormalized,
      from: RouteLocationNormalized,
      next: NavigationGuardNext,
    ): void => {
      NProgress.start()
      next()
    },
  )

  router.afterEach((): void => {
    // 添加短暂延迟以避免闪烁
    setTimeout(() => {
      NProgress.done()
    }, 50)
  })
})
