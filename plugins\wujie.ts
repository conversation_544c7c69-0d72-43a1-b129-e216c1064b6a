
type MinimalNuxtApp = { hook: (name: string, cb: () => void) => void };

export default defineNuxtPlugin((nuxtApp: MinimalNuxtApp) => {

  if (process.server) return;
  if (typeof window === 'undefined') return;
  if (!window.__POWERED_BY_WUJIE__) return;

  // 为Wujie环境设置公共路径，确保动态导入正确工作
  if (window.__WUJIE_PUBLIC_PATH__) {
    // 确保Vite的动态导入能正确解析路径
    const script = document.createElement('script')
    script.textContent = `window.__vite_public_path__ = "${window.__WUJIE_PUBLIC_PATH__}";`
    document.head.appendChild(script)
  }

  // 子应用生命周期改造
  window.__WUJIE_MOUNT = () => {
    const root = document.getElementById('__nuxt') as HTMLElement | null;
    if (root) root.style.display = '';
  };
  window.__WUJIE_UNMOUNT = () => {
    const root = document.getElementById('__nuxt') as HTMLElement | null;
    if (root) root.style.display = 'none';
  };

  // 主动调用无界的 mount，避免时机不一致
  const wujieInstance = window.__WUJIE;
  if (wujieInstance && typeof wujieInstance.mount === 'function') {
    wujieInstance.mount();
  }

  // 获取path
  const wujie = window.$wujie;
  // 与主应用通信：仅当 $wujie 存在时绑定 bus
  const bus = wujie?.bus as {
    $on: (event: string, handler: (...args: any[]) => void) => void;
    $off: (event: string, handler: (...args: any[]) => void) => void;
    $emit: (event: string, ...args: any[]) => void;
  } | undefined;

  const handleRouteUpdate = (nextRoute: any) => {
    if (!nextRoute.path) return;
    const finalPath = nextRoute.path.startsWith('/') ? nextRoute.path : `/${nextRoute.path}`;
    navigateTo(finalPath, { replace: true });
  };

  if (bus && typeof bus.$on === 'function') {
    bus.$on('ai:path-change', handleRouteUpdate);
    nuxtApp.hook('app:beforeUnmount', () => {
      bus.$off && bus.$off('ai:path-change', handleRouteUpdate);
    });

    // router.afterEach((to: any) => {
    //   bus.$emit('route-change', to.fullPath);
    // });
  }
});


