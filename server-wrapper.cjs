// server-wrapper.cjs
const { spawn } = require('child_process')

const nodePath = '/var/lib/jenkins/.nvm/versions/node/v20.11.1/bin/node'
const serverPath = './.output/server/index.mjs'

const server = spawn(nodePath, [serverPath], {
  stdio: 'inherit',
  env: process.env,
})

server.on('close', code => {
  process.exit(code)
})

process.on('SIGINT', () => {
  server.kill('SIGINT')
})

process.on('SIGTERM', () => {
  server.kill('SIGTERM')
})
