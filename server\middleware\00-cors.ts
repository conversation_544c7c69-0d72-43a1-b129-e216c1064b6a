// 统一CORS中间件 - 处理所有请求的CORS头并允许静态资源缓存
// 合并了原有的01-cors.ts和static-cors.ts功能
// 确保无界微前端能够正确加载静态资源
// 参考: https://wujie-micro.github.io/doc/question/

export default defineEventHandler(async event => {
  const origin = getHeader(event, 'origin')
  const url = getRequestURL(event)
  const method = getMethod(event)

  // 开发模式强制CORS处理 - 确保dev模式下也能正确应用
  const isDev = process.env.NODE_ENV === 'development'

  // 检查是否为允许的域名
  const isAllowedOrigin = (originUrl: string | undefined): boolean => {
    if (!originUrl) return true // 无origin的请求（如同源请求）直接允许

    return (
      // plasdata.com及其子域名
      /^https?:\/\/([a-z0-9-]+\.)*plasdata\.com(:[0-9]+)?$/i.test(originUrl) ||
      // localhost (任意端口)
      /^https?:\/\/localhost(:[0-9]+)?$/i.test(originUrl) ||
      // 127.0.0.1 (任意端口)
      /^https?:\/\/127\.0\.0\.1(:[0-9]+)?$/i.test(originUrl)
    )
  }

  // 获取允许的Origin值
  const getAllowedOrigin = (requestOrigin: string | undefined): string => {
    if (!requestOrigin) return 'http://localhost:3000' // 默认值
    if (isAllowedOrigin(requestOrigin)) return requestOrigin
    return 'http://localhost:3000' // 不允许的Origin返回默认值
  }

  // 调试日志 - 追踪Vue样式文件请求
  if (url.pathname.includes('.vue') && url.search.includes('type=style')) {
    // eslint-disable-next-line no-console
    console.log('🎨 Vue样式文件请求:', {
      url: url.href,
      pathname: url.pathname,
      search: url.search,
      origin: origin,
      method: method,
      isDev: isDev,
      timestamp: new Date().toISOString(),
    })
  }

  // 强制为所有静态资源设置CORS头 - 在任何其他处理之前
  // 无界微前端要求：当credentials为include时，不能使用通配符*
  if (
    url.pathname.startsWith('/_nuxt/') ||
    (isDev &&
      (url.pathname.startsWith('/assets/') || url.pathname.includes('.vue')))
  ) {
    const allowOrigin = getAllowedOrigin(origin) // 使用新的域名检查函数

    // 在开发模式下更加强制地设置CORS头
    const corsHeaders = {
      'Access-Control-Allow-Origin': allowOrigin,
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400',
      Vary: 'Origin',
    }

    // 强制设置每个头，确保不被其他中间件覆盖
    Object.entries(corsHeaders).forEach(([key, value]) => {
      event.node.res.setHeader(key, value)
      setHeader(event, key, value)
    })

    if (url.pathname.includes('.vue') && url.search.includes('type=style')) {
      // eslint-disable-next-line no-console
      console.log('🔧 强制设置静态资源CORS头:', {
        origin: allowOrigin,
        isDev: isDev,
        headers: event.node.res.getHeaders(),
      })
    }
  }

  // 检查是否为静态资源请求
  const isStaticResource =
    url.pathname.startsWith('/_nuxt/') ||
    url.pathname.startsWith('/assets/') ||
    url.pathname.startsWith('/public/') ||
    url.pathname.startsWith('/chunks/') ||
    url.pathname.startsWith('/entry/') ||
    url.pathname.startsWith('/img/') ||
    url.pathname.startsWith('/fonts/') ||
    url.pathname.match(
      /\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot|webp|map)$/
    ) ||
    // 特殊处理Vue单文件组件样式文件（带查询参数的CSS）
    (url.pathname.includes('.vue') && url.search.includes('type=style')) ||
    url.search.includes('lang.css')

  // 允许所有来源 - 不做任何限制
  const allowThisRequest = true

  // 优先设置静态资源缓存头（在CORS头之前）
  if (isStaticResource) {
    // 静态资源允许缓存
    if (url.pathname.match(/\.(js|css)$/)) {
      // JS和CSS文件长期缓存
      setHeader(event, 'Cache-Control', 'public, max-age=31536000, immutable')
      setHeader(event, 'ETag', `"${Date.now()}"`) // 添加ETag以支持条件请求
    } else if (
      url.pathname.match(
        /\.(png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot|webp)$/
      )
    ) {
      // 图片和字体文件中期缓存
      setHeader(event, 'Cache-Control', 'public, max-age=86400')
      setHeader(event, 'ETag', `"${Date.now()}"`) // 添加ETag以支持条件请求
    } else {
      // 其他静态资源短期缓存
      setHeader(event, 'Cache-Control', 'public, max-age=3600')
      setHeader(event, 'ETag', `"${Date.now()}"`) // 添加ETag以支持条件请求
    }
  }

  // 设置CORS头 - 无界微前端兼容配置
  const allowOrigin = getAllowedOrigin(origin) // 使用新的域名检查函数
  const corsHeaders = {
    'Access-Control-Allow-Origin': allowOrigin,
    'Access-Control-Allow-Methods': isStaticResource
      ? 'GET, HEAD, OPTIONS'
      : 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Credentials': 'true', // 无界微前端需要支持凭据
    'Access-Control-Max-Age': '86400',
    Vary: 'Origin', // 重要：告诉代理/缓存根据Origin区分响应
  }

  Object.entries(corsHeaders).forEach(([key, value]) => {
    setHeader(event, key, value)
    event.node.res.setHeader(key, value)
  })

  // 调试日志 - 确认CORS头设置
  if (url.pathname.includes('.vue') && url.search.includes('type=style')) {
    // eslint-disable-next-line no-console
    console.log('✅ CORS头已设置:', {
      isStaticResource,
      corsHeaders,
      actualHeaders: Object.fromEntries(
        Object.keys(corsHeaders).map(key => [
          key,
          getResponseHeader(event, key),
        ])
      ),
    })
  }

  // 处理OPTIONS预检请求 - 无界微前端兼容配置
  if (method === 'OPTIONS') {
    setResponseStatus(event, 200)
    const allowOrigin = getAllowedOrigin(origin) // 使用新的域名检查函数
    event.node.res.setHeader('Access-Control-Allow-Origin', allowOrigin)
    event.node.res.setHeader(
      'Access-Control-Allow-Methods',
      isStaticResource
        ? 'GET, HEAD, OPTIONS'
        : 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD'
    )
    event.node.res.setHeader('Access-Control-Allow-Headers', '*')
    event.node.res.setHeader('Access-Control-Allow-Credentials', 'true') // 支持凭据
    event.node.res.setHeader('Access-Control-Max-Age', '86400')
    event.node.res.setHeader('Vary', 'Origin')

    if (url.pathname.includes('.vue') && url.search.includes('type=style')) {
      // eslint-disable-next-line no-console
      console.log('🚦 OPTIONS预检请求处理 Vue样式文件:', {
        url: url.href,
        origin: allowOrigin,
        headers: event.node.res.getHeaders(),
      })
    }

    event.node.res.end()
    return
  }

  // 非静态资源禁用缓存，避免浏览器缓存没有CORS头的响应
  if (!isStaticResource) {
    event.node.res.setHeader(
      'Cache-Control',
      'no-cache, no-store, must-revalidate'
    )
    event.node.res.setHeader('Pragma', 'no-cache')
    event.node.res.setHeader('Expires', '0')

    // 强制覆盖可能的缓存头
    event.node.res.removeHeader('etag')
    event.node.res.removeHeader('last-modified')
  }

  // 开发模式下的最终CORS头检查 - 确保响应一定包含CORS头
  if (isDev && origin && url.pathname.startsWith('/_nuxt/')) {
    // 在响应发送前再次确认CORS头存在
    event.node.res.on('pipe', () => {
      const currentOrigin = event.node.res.getHeader(
        'Access-Control-Allow-Origin'
      )
      if (!currentOrigin || currentOrigin === '') {
        // eslint-disable-next-line no-console
        console.log('⚠️  CORS头缺失，强制补充:', url.pathname)
        const fallbackOrigin = getAllowedOrigin(origin) // 使用新的域名检查函数
        event.node.res.setHeader('Access-Control-Allow-Origin', fallbackOrigin)
        event.node.res.setHeader('Access-Control-Allow-Credentials', 'true')
        event.node.res.setHeader('Vary', 'Origin')
      }
    })
  }
})
