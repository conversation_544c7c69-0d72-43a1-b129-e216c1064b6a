import { defineStore } from 'pinia'
import { aiChatIndexApi } from '@/api/ai/index.ts'
import { TOOL_TYPE } from '@/types/ai/enums.ts'
import type { KnowledgeItem, ModelConfig } from '@/types/ai/interface.ts'

export const useMainStore = defineStore('main', {
  state: () => ({}),
  actions: {},
})

export const useAiStore = defineStore('ai', {
  state: () => ({
    currentSessionId: '',
    selectedToolType: TOOL_TYPE.CHAT as TOOL_TYPE | undefined,
    modelList: [] as ModelConfig[],
    mineKnowledges: [] as KnowledgeItem[],
    recommendKnowledges: [] as KnowledgeItem[],
  }),

  actions: {
    setCurrentSessionId(id: string) {
      this.currentSessionId = id
    },
    setSelectedToolType(type: TOOL_TYPE) {
      this.selectedToolType = type
    },
    async loadChatIndex() {
      try {
        const { data } = await aiChatIndexApi()
        this.modelList = (data.models || []) as ModelConfig[]
        this.mineKnowledges = (data.mine_knowledges || []) as KnowledgeItem[]
        this.recommendKnowledges = (data.recommend_knowledges || []) as KnowledgeItem[]
      } catch (error) {
        console.error('loadChatIndex error:', error)
        this.modelList = []
        this.mineKnowledges = []
        this.recommendKnowledges = []
      }
    },
  },
})
