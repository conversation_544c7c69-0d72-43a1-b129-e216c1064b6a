import { defineStore } from 'pinia'

const defaultInfo = {
  company: [],
  category: [],
  feature: [],
  filler: [],
  property: [],
  usage: [],
}

export const useSearchStore = defineStore('search', {
  state: () => ({
    filterInfo: { ...defaultInfo },
    mustNotFiller: -1, // -1默认，0不包含任何填充物
    keyword: '',
  }),
  getters: {
    // 返回筛选项
    getFilterInfo(state) {
      return state.filterInfo
    },
    // 返回关键词
    getKeyword(state) {
      return state.keyword
    },
  },
  actions: {
    updateFilterState(value: 0 | -1) {
      this.mustNotFiller = value
    },
    // 更新筛选项
    addFilterItem(filterItem: any) {
      const itemList = filterItem.optionInfo
        ? [filterItem.optionInfo]
        : [...filterItem.list]
      const uniqueItems: (typeof filterItem.optionInfo)[] = itemList.filter(
        item =>
          !this.filterInfo[filterItem.typeKey].some(({ id, name }) =>
            filterItem.typeKey === 'usage'
              ? id === item.id && name === item.name
              : id === item.id,
          ),
      )
      if (!uniqueItems.length) return
      this.filterInfo[filterItem.typeKey].push(...uniqueItems)
    },
    // 替换筛选项
    replaceFilterItem(filterItem: any) {
      this.filterInfo[filterItem.typeKey] = filterItem.list
    },
    // 删除单个筛选项
    deleteFilterItem({
      typeKey,
      optionInfo,
    }: {
      typeKey: keyof typeof defaultInfo;
      optionInfo: any;
    }) {
      this.filterInfo[typeKey] = this.filterInfo[typeKey].filter(
        item => item.name !== optionInfo.name,
      )
    },
    // 清空筛选项
    clearFilter(typeKey?: keyof typeof defaultInfo) {
      if (typeKey) {
        this.filterInfo[typeKey] = []
        return
      }
      this.filterInfo = JSON.parse(JSON.stringify(defaultInfo))
    },

    // 重置搜索条件
    resetSearch() {
      this.clearFilter()
      this.keyword = ''
      this.mustNotFiller = -1
    },

    // 更新关键词
    updateKeyword(keyword: string) {
      this.keyword = keyword
    },
  },
})
