import Cookies from 'js-cookie'
import { defineStore } from 'pinia'
import { logoutRequest, userInfoRequest } from '@/api/auth/index.ts'
import { FULL_REGISTRATION_ROLE } from '@/constants/user-role.ts'

// 默认用户信息
export const defaultInfo = {
  nick_name: '', // 昵称
  name: '', // 姓名
  first_name: '',
  last_name: '',
  gender: 1, // 性别
  email: '', // 电子邮箱
  sk_mobile: '', // 联系电话
  company: '', // 公司名称
  business: '', // 主营业务
  company_type: '', // 公司类型
  industry: '', // 行业
  job_title: '', // 公司职位
  address: '', // 地址
  address2: '',
  country: '', // 国家/区域
  province: '', // 省份
  city: '', // 城市
  area: '', // 地区
  postal_code: '', // 邮政编码
  region: [],
  is_supply: 0, // 是否供应
  agent_brand: '', // 代理品牌
  avatar_url: '', // 头像
  auth_role: 0, // 账号类型（游客、完全注册、无价值）
  demand_subscribe: false, // 需求订阅
  total_point: 0, // 总点数
  sk_email: '',
  enterprise: {
    is_business_user: false, // 是否开启企业版
    is_super_business_user: 0, // 是否超级用户管理者
    company_name: '', // 企业名
  },
  points_data: {
    charge_record: [], // 获得记录
    usage_record: [], // 消耗记录
    points_freezing: 0, // 冻结点数
    points_consumed: 0, // 消耗点数
    points_remaining: 0, // 可用点数
  },
  enterprise_points_data: {
    charge_record: [], // 获得记录
    usage_record: [], // 消耗记录

    enterprise_points_freezing: 0, // 企业冻结点数
    enterprise_points_remaining: 0, // 企业剩余点数
    week_points_limit: 0, // 个人每周限额
    week_points_remaining: 0, // 个人每周剩余
    person_points_freezing: 0, // 个人本周消耗点数
  },
}
export const useUserStore = defineStore('user', {
  state: () => ({
    info: { ...defaultInfo },
  }),
  getters: {
    isLogin(state) {
      const token = useCookie('token')
      return !!token.value
    },
    // 是否为完全注册
    isFullRole(state) {
      return state.info?.auth_role === FULL_REGISTRATION_ROLE
    },
  },
  actions: {
    // 更新用户信息
    async updateUserInfo(userInfo?: any) {
      // 参数传递了用户信息直接更新
      if (userInfo) {
        this.info = { ...userInfo }
        return
      }
      // 通过token请求用户信息并更新
      if (!this.isLogin) {
        // console.warn('未登录状态，无法获取用户信息')
        return
      }
      const { success, data, message } = await userInfoRequest()
      if (!success) {
        console.warn(message)
        return
      }
      this.info = data
    },
    // 注销
    async logout(reload = true) {
      if (window.$wujie) {
        window.$wujie.bus.$emit('logout')
        return
      }

      const { success, message } = await logoutRequest()
      if (!success) {
        console.warn(message)
        return
      }
      if (process.client && !reload) {
        navigateTo('/')
      }
      this.info = { ...defaultInfo }
      localStorage.clear()
      Cookies.remove('token')
      Cookies.remove('sk_base_auth_token')
      Cookies.set('sk_base_has_login', '0', { expires: 1 })
      if (process.client && reload) {
        window.location.href = location.href
      }
    },
  },
})
