import { MARK_TYPE, TOOL_TYPE, UPLOAD_MODE } from '@/types/ai/enums.ts'
type KnowledgeItem = {
  // file: string;
  // fileIcon: string;
  id: string;
  name: string;
  thumb: string;
  desc: string;
  follow_count: number;
  file_count: number;
  creator: string;
  type: string;
  is_official: boolean;
  [key: string]: any;
}

type ModelConfig = {
  name: string;
  desc: string;
  upload_mode: UPLOAD_MODE[];
}

type HistoryItem = {
  title: string;
  session_id: string;
  created_at: string;
  updated_at: string;
}

type MessageItem = {
  content: string;
  reasoning?: string;
  assistant_id?: string;
  mark?: MARK_TYPE;
  data: {
    type?: string;
    knowledge?: KnowledgeItem[];
    [key: string]: any;
  };
  [key: string]: any;
}

type ToolItem = {
  type: TOOL_TYPE;
  name: string;
  icon: string;
  component: Component;
  fun: (() => void) | null;
}

export type { MessageItem, HistoryItem, KnowledgeItem, ToolItem, ModelConfig }
