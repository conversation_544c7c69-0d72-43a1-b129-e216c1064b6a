declare module '@/api/urls' {
  const urls: any
  export default urls
}

declare module '@/composables/useHttp' {
  export function getGetModel(url: string, params?: any): () => Promise<any>
  export function getPostModel(url: string, data?: any): () => Promise<any>
}

// 包含其他类型声明文件
/// <reference path="./nuxt.d.ts" />
/// <reference path="./requests.d.ts" />
/// <reference path="./store.d.ts" />
/// <reference path="./process.d.ts" />
/// <reference path="./wujie.d.ts" />

declare module 'js-cookie' {
  interface CookieAttributes {
    expires?: number | Date;
    path?: string;
    domain?: string;
    secure?: boolean;
    sameSite?: 'strict' | 'lax' | 'none';
    [property: string]: any;
  }

  interface CookiesStatic {
    defaults: CookieAttributes;
    set(name: string, value: string | object, options?: CookieAttributes): void;
    get(name: string): string | undefined;
    get(): { [key: string]: string };
    remove(name: string, options?: CookieAttributes): void;
    withAttributes(attributes: CookieAttributes): CookiesStatic;
    withConverter(converter: {
      read(value: string): string;
      write(value: string): string;
    }): CookiesStatic;
  }

  const Cookies: CookiesStatic
  export default Cookies
}

