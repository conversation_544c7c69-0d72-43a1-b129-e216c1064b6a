import {
  MessageBoxOptions,
  MessageOptions,
  MessageType,
} from '~/constants/message-type'

interface SKToastInterface {
  info: (message: string, options?: Partial<MessageOptions>) => void;
  success: (message: string, options?: Partial<MessageOptions>) => void;
  warning: (message: string, options?: Partial<MessageOptions>) => void;
  error: (message: string, options?: Partial<MessageOptions>) => void;
  show: (options: MessageOptions | string) => void;
}

interface SKConfirmInterface {
  info: (
    message: string,
    options?: Partial<MessageBoxOptions>
  ) => Promise<boolean>;
  success: (
    message: string,
    options?: Partial<MessageBoxOptions>
  ) => Promise<boolean>;
  warning: (
    message: string,
    options?: Partial<MessageBoxOptions>
  ) => Promise<boolean>;
  error: (
    message: string,
    options?: Partial<MessageBoxOptions>
  ) => Promise<boolean>;
  alert: (
    message: string,
    options?: Partial<MessageBoxOptions>
  ) => Promise<boolean>;
  show: (options: MessageBoxOptions | string) => Promise<boolean>;
}

declare module '#app' {
  interface NuxtApp {
    $SKToast: SKToastInterface;
    $SKConfirm: SKConfirmInterface;
  }
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $SKToast: SKToastInterface;
    $SKConfirm: SKConfirmInterface;
  }
}

declare module '~/components/message' {
  import { App } from 'vue'

  const MessagePlugin: {
    install: (app: App) => void;
  }

  export default MessagePlugin

  export const SKToast: SKToastInterface

  export const SKConfirm: SKConfirmInterface

  export { MessageType }
}
