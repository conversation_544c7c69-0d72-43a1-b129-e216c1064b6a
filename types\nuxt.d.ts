// Nuxt特有模块的类型声明

// #app模块及其导出类型
declare module '#app' {
  export * from '@nuxt/schema'

  // useFetch相关类型
  export interface UseFetchOptions<DataT> {
    baseURL?: string;
    method?: string;
    headers?: Record<string, string>;
    body?: Record<string, any>;
    params?: Record<string, any>;
    immediate?: boolean;
    server?: boolean;
    lazy?: boolean;
    onRequest?: (context: any) => void;
    onResponse?: (context: { response: any }) => void;
    onResponseError?: (context: { response: any }) => void;
    [key: string]: any;
  }

  // 其他Nuxt composables类型
  export function useCookie<T>(name: string, options?: any): Ref<T | null>
  export function useFetch<T>(
    url: string,
    options?: UseFetchOptions<T>
  ): {
    data: Ref<T | null>;
    error: Ref<any>;
    status: Ref<string>;
    execute: () => Promise<void>;
    pending: Ref<boolean>;
  }
  export function useNuxtApp(): any
  export function useRequestHeaders(headers?: string[]): Record<string, string>
}

// 为了支持Ref类型
import type { Ref } from 'vue'
