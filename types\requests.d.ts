// 请求相关模块的类型声明

/**
 * 请求状态码模块
 */
declare module '@/constants/request-code' {
  const requestCode: {
    SUCCESS: number; // 成功
    FAILURE: number; // 失败
    NOT_FOUND: number; // 资源不存在
    FORBIDDEN: number; // 禁止访问
    UNAUTHORIZED: number; // 未授权
    NEED_LOGIN: number; // 需要登录
    INVALID_PARAMS: number; // 参数无效
    SERVER_ERROR: number; // 服务器错误
    // 可能的其他状态码
    [key: string]: number;
  }

  export default requestCode
}

/**
 * 开发环境配置
 */
declare module '@/env/env.dev' {
  const env: {
    base: string; // API基础路径
    socket: string; // WebSocket路径
    fileUpload: string; // 文件上传路径
    // 其他环境变量
    [key: string]: string;
  }

  export default env
}

/**
 * 生产环境配置
 */
declare module '@/env/env.pro' {
  const env: {
    base: string; // API基础路径
    socket: string; // WebSocket路径
    fileUpload: string; // 文件上传路径
    // 其他环境变量
    [key: string]: string;
  }

  export default env
}
