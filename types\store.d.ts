// Store相关模块的类型声明

/**
 * 用户Store模块
 */
declare module '@/store/user' {
  import { defineStore } from 'pinia'

  export interface UserInfo {
    id?: string;
    username?: string;
    nickname?: string;
    avatar?: string;
    email?: string;
    phone?: string;
    roles?: string[];
    permissions?: string[];
    [key: string]: any;
  }

  export interface UserState {
    token: string | null;
    userInfo: UserInfo;
    isLogin: boolean;
  }

  // 用户Store方法
  export interface UserStore {
    logout(): Promise<void>;
    updateUserInfo(): Promise<void>;
    setToken(token: string): void;
    clearUserInfo(): void;
  }

  // 简化导出，使用any避免类型冲突
  export const useUserStore: any
}
