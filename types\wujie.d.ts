// 无界微前端全局变量类型声明

// 注入到 window.$wujie 的对象结构
export interface WujieInjection {
  bus?: any;
  shadowRoot?: ShadowRoot;
  props?: { path?: string; [key: string]: any };
  location?: any;
}

// 显式表达：当 __POWERED_BY_WUJIE__ 为 true 时，$wujie 一定存在
export type WujieEnabledWindow = Window & {
  __POWERED_BY_WUJIE__: true;
  $wujie: WujieInjection;
};

// 对 window 的更精确建模：两种状态的联合类型
// 使用方式：
// const w = window as WujieWindow;
// if (w.__POWERED_BY_WUJIE__) { w.$wujie /* 已强类型存在 */ }
export type WujieWindow =
  | (Window & { __POWERED_BY_WUJIE__: true; $wujie: WujieInjection })
  | (Window & { __POWERED_BY_WUJIE__?: false | undefined; $wujie?: undefined });

declare global {
  interface Window {
    // 是否存在无界
    __POWERED_BY_WUJIE__?: boolean;
    // 子应用公共加载路径
    __WUJIE_PUBLIC_PATH__?: string;
    // 原生的querySelector
    __WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__?: typeof Document.prototype.querySelector;
    // 原生的querySelectorAll
    __WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__?: typeof Document.prototype.querySelectorAll;
    // 原生的window对象
    __WUJIE_RAW_WINDOW__?: Window;
    // 子应用沙盒实例
    __WUJIE?: any;
    // 子应用mount函数
    __WUJIE_MOUNT?: () => void;
    // 子应用unmount函数
    __WUJIE_UNMOUNT?: () => void | Promise<void>;
    // 注入对象
    $wujie?: WujieInjection;
  }
}

export {}
