import { classifiedTypes as FILTER_TYPE } from '@/constants/classified-type.ts'

/**
 * 返回新api中的筛选格式
 * @returns {Object}
 */
function getFilterParams(selectedInfo) {
  return Object.keys(selectedInfo).reduce((acc, key) => {
    // if (!selectedInfo[key].length) {
    //   return acc
    // }
    if (key === 'filler') {
      acc[key] = selectedInfo[key].map(item => ({
        id: item.id,
        val_left: item.value?.[0],
        val_right: item.value?.[1],
        must_not: item.must_not,
      }))
      return acc
    }
    if (key === 'feature') {
      acc[key] = selectedInfo[key].map(item => ({
        id: item.id,
        val_left: item.value,
        val_right: item.id === 8 ? '' : item.value, // pcr(>=) 特殊处理
      }))
      return acc
    }
    if (key === 'property') {
      function fun(obj) {
        return {
          header_id: obj.header_id,
          value: obj.value,
          condition: obj.condition,
          level: obj.level,
        }
      }
      acc.property = selectedInfo[key]
        .filter(item => item.level === 3)
        .map(obj => fun(obj))
      acc.property_v1 = selectedInfo[key]
        .filter(item => item.level === 2)
        .map(obj => fun(obj))

      return acc
    }
    if (key === 'usage') {
      acc.classification_first = selectedInfo[key]
        .filter(item => item.level === 1)
        .map(item => item.id)
      acc.classification_second = selectedInfo[key]
        .filter(item => item.level === 2)
        .map(item => item.id)
      acc.classification_third = selectedInfo[key]
        .filter(item => item.level === 3)
        .map(item => item.id)

      return acc
    }
    acc[key] = selectedInfo[key].map(item => item.id)
    return acc
  }, {})
}

/**
 * 返回旧api中的筛选格式
 * @returns {Object}
 */
function getFilterParamsOld(selectedInfo) {
  return Object.keys(this.selectedInfo).reduce((acc, key) => {
    if (selectedInfo[key].length) {
      acc[key] = selectedInfo[key]
    }
    return acc
  }, {})
}

/**
 * 返回带参数的筛选项
 * @returns {Object}
 */
function getRenderFilter({
  type: filterType,
  item: filterItem,
  show = false,
  all = false,
}) {
  // 返回全部字段 / 只返回显示字段
  function returns(text) {
    if (all) {
      return {
        show: text,
        ...filterItem,
      }
    }
    return text
  }

  // 无对应类型直接返回
  if (!Object.keys(FILTER_TYPE).includes(filterType)) {
    return returns(filterItem.name)
  }

  const filterName = show ? `${FILTER_TYPE[filterType].name}：` : ''

  if (filterType === 'property') {
    // 提取 性能参数内容，数值，测试条件(数组)
    const { name, value, condition } = filterItem

    // 解析 value 数值部分
    const metricVal = handelValues(value) // 数值部分，例： 5 g/cm3
    // 解析 condition 测试条件部分，数组形式，格式跟 value 一致
    const conditionValList = []
    // 取出有数值的测试条件
    if (condition && condition.length) {
      condition.forEach(item => {
        if ([item.val_left, item.val_right, item.val_str].some(i => !!i)) {
          conditionValList.push(item)
        }
      })
    }
    // 拼接测试条件
    const conditionVal = conditionValList.length
      ? `(${conditionValList
          .map(conditionItem => handelValues(conditionItem))
          .join(', ')})`
      : ''

    // 存在测试参数或测试条件才有 ：
    const colon = conditionVal === '' && metricVal === '' ? '' : ': '

    // ${name}: ${metricVal} ${conditionVal}  ==> 可见光透过率: ≥ 1 %
    return returns(`${filterName}${name}${colon}${metricVal} ${conditionVal}`)
  }

  if (filterType === 'filler') {
    // 解析 填充参数
    const { name, value, logic, must_not: mustNot } = filterItem
    if (mustNot) {
      return returns(`不包含 ${filterName}${name}`)
    }

    const metricVal = fillerValues(value, logic)

    //  `${name}: ${metricVal}`  ==>   Cotton(棉): ≥ 123%
    return returns(`${filterName}${name} ${metricVal}`)
  }

  if (filterType === 'feature') {
    // 解析 特性参数
    const { name, value } = filterItem

    // 存在参数才有
    const colon = value ? ': ' : ''
    const logic = value ? '%' : ''

    //  `${name}: ${value}`  ==>  循环再生利用(PCR)(>=): 100
    return returns(`${filterName}${name}${colon}${value || ''}${logic}`)
  }
  // 种类、公司
  return returns(`${filterName}${filterItem.name}`)
}

/**
 * 解析 性能参数 数值部分: metric，condition 等
 */
function handelValues(valObj) {
  // 解析数值部分
  const {
    val_left: valLeft,
    val_right: valRight,
    val_str: valStr,
    val_unit: valUnit,
  } = valObj
  const metricSep = ' ' // 数值 分隔符
  const unit = valUnit === '-' ? '' : ` ${valUnit}` // 单位

  // ==== valStr 有值，则为等值模式 5 kg ====
  if (valStr) {
    return [valStr, unit].join(metricSep)
  }

  // ==== valStr 无值，则为范围模式 1 - 2 kg ====
  if (valLeft && valRight) {
    // 范围 10 - 20 g
    return [valLeft, '-', valRight, unit].join(metricSep)
  }

  if (valLeft && !valRight) {
    // ≥ 10 g
    return ['≥', valLeft, unit].join(metricSep)
  }

  if (!valLeft && valRight) {
    // ≤ 20 g
    return ['≤', valRight, unit].join(metricSep)
  }
  return ''
}

/**
 * 解析 填充物 数值部分
 */
function fillerValues(valList, logic, mustNot) {
  if (mustNot) return '不包含'
  const metricSep = ' ' // 数值 分隔符
  if (logic === '-') {
    // 范围 10 - 20 g
    if (valList[0] === '' && valList[1] === '') return '任意比例'
    return [valList[0], logic, valList[1], '%'].join(metricSep)
  }

  if (logic === '=') {
    // = 10 g
    if (valList[0] === '') return '任意比例'
    return [logic, valList[0], '%'].join(metricSep)
  }

  if (logic === '>=') {
    // ≥ 10 g
    if (valList[0] === '') return '任意比例'
    return ['≥', valList[0], '%'].join(metricSep)
  }

  if (logic === '<=') {
    // ≤ 20 g
    if (valList[1] === '') return '任意比例'
    return ['≤', valList[1], '%'].join(metricSep)
  }
  return '任意比例'
}

export { getFilterParams, getFilterParamsOld, getRenderFilter, fillerValues }
