/*
 * @Description:
 * @Version: 1.0
 * @Autor: zencyhu
 * @Date: 2021-07-28 12:34:54
 * @LastEditors: zencyhu
 * @LastEditTime: 2021-08-06 14:38:50
 */

import axios from 'axios'
import cookies from 'js-cookie'
import qs from 'qs'
import { v4 as uuidv4 } from 'uuid'

export type HttpPointOptions = {
  key_id: string;
  extend_value?: any;
  axios?: boolean;
  [property: string]: any;
}

export default async function (
  url: string,
  options: HttpPointOptions = { key_id: '', axios: false },
) {
  if (!url) throw new Error('url is required!')
  if (!options.key_id) throw new Error('options.key_id is required!')

  const sessionExpiredStr = localStorage.getItem('sk_session_id_expired')
  const sessionExpired = sessionExpiredStr ? parseInt(sessionExpiredStr) : 0
  let sessionId = localStorage.getItem('sk_session_id')
  const timestamp = new Date().getTime()

  if (!sessionId || sessionExpired < timestamp) {
    sessionId = uuidv4()
    localStorage.setItem('sk_session_id', sessionId)
  }

  localStorage.setItem('sk_session_id_expired', timestamp + 1000 * 60 * 5 + '') // 5分钟会话时间

  const path = `${url}?${qs.stringify({
    session_id: sessionId, // 会话 ID
    client_id: cookies.get('sk_client_id'), // 客户端 ID
    timestamp, // 时间戳
    ...options, // 额外数据
  })}`

  !options.axios ? (new Image().src = path) : await axios.get(path)
}
