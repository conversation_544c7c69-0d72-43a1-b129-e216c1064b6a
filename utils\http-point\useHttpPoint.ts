import Cookies from 'js-cookie'
import envDev from '@/env/env.dev'
import envPro from '@/env/env.pro'
import type { HttpPointOptions } from './baseHttpPoint.ts'
import httpPoint from './baseHttpPoint.ts'

const urls: Record<string, string> = {
  production: envPro.base,
  development: envDev.base,
}

const DOMAIN_NAME =
  urls[process.env.NODE_ENV as keyof typeof urls] || envDev.base
const path = '/analysis/fetch'

export default function HttpPoint(options: HttpPointOptions): void {
  // 确保只在客户端环境下执行
  if (typeof window === 'undefined') return

  // 确保 options 存在并且包含必要的 key_id
  if (!options || !options.key_id) {
    console.error('HttpPoint: 缺少必要的参数 key_id')
    return
  }

  try {
    httpPoint(DOMAIN_NAME + path, {
      referer: window.location.pathname,
      ...options,
      platform: 2,
      fvt: Cookies.get('sk_fvt') || -1,
    })
  } catch (e) {
    console.error('HttpPoint 错误:', (e as Error).message)
  }
}
