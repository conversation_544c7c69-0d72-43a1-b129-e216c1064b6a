// 判断字符串是否为邮箱
export const isEmail = (str: string) => {
  const reg =
    /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/
  return reg.test(str)
}

export const PWD_REG = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9]{8,14}$/

export const PHONE_REG = /^1[3-9]\d{9}$/

export const EMAIL_REG = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/

export const NAME_REG = /[^a-zA-Z0-9·\u4E00-\u9FA5\s]+/ // 校验输入的姓名是否包含除“·”以外的特殊字符
